import { ApiProperty } from '@nestjs/swagger';
import { AutoMap } from '@automapper/classes';
import { Condition } from '@common/enums/condition.enum';

export class CreateItemAuditDto {
  @AutoMap()
  @ApiProperty()
  storeId: number;

  @AutoMap()
  @ApiProperty()
  itemId: number;

  @AutoMap()
  @ApiProperty()
  quantityLeased: number;

  @AutoMap()
  @ApiProperty()
  quantityReleased?: number;

  @AutoMap()
  @ApiProperty()
  quantityReturned?: number;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    enum: Object.values(Condition),
    name: 'conditionBeforeLease',
    description: 'Possible item conditions',
    default: Condition.NOT_SPECIFIED,
  })
  conditionBeforeLease: Condition;

  @AutoMap()
  @ApiProperty()
  leasedDate: Date;

  @AutoMap()
  @ApiProperty()
  returnedDate?: Date;
}
