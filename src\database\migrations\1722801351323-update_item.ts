import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateItem1722801351323 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.changeColumn(
      'item',
      'quantity',
      new TableColumn({
        name: 'actual_quantity',
        type: 'bigint',
        default: 0,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.changeColumn(
      'item',
      'actual_quantity',
      new TableColumn({
        name: 'quantity',
        type: 'bigint',
        default: 0,
      }),
    );
  }
}
