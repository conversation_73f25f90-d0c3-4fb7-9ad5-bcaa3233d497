import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityStatus } from '@common/entities/base.entity';
import { LoggerService } from '@common/logger/logger.service';
import { CoreUtils } from '@common/utils/core.utils';
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  NotFoundException,
  // Res,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import { UserProfileDto } from '../user/dto/user-profile.dto';
import { VerifyUserDto } from '../user/dto/verify-user.dto';
import { User } from '../user/entities/user.entity';
import { UserService } from '../user/user.service';
import { ChangePasswordDto } from './dto/change-password.dto';
import { OnboardUserDto } from './dto/onboard-user.dto';
import { JwtPayload } from './types/jwt-payload.type';
import { Tokens } from './types/token.type';
import { MailService } from '@core/notification/mail-service/mail.service';

@Injectable()
export class AuthenticationService {
  constructor(
    private readonly logger: LoggerService,
    private readonly jwtService: JwtService,
    private readonly userService: UserService,
    private readonly configService: ConfigService,
    private readonly mailService: MailService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(AuthenticationService.name);
  }

  /**
   * Signup method. This method is used to create a new user. It checks if the user already exists and if not, it creates a new user.
   * @param dto - OnboardUserDto
   */
  async signup(dto: OnboardUserDto): Promise<Record<string, any>> {
    const existingUser = await this.userService.findByEmail(dto.email);
    if (existingUser) {
      this.logger.error('User already exists');
      throw new ForbiddenException('User already exists');
    }

    let onboardedUser = await this.classMapper.mapAsync(
      dto,
      OnboardUserDto,
      User,
    );

    onboardedUser.password = CoreUtils.hash(dto.password);
    const token = await this.generateTokens(
      onboardedUser.id,
      onboardedUser.email,
    );
    onboardedUser.refreshToken = token.refreshToken;
    onboardedUser = await this.userService.create(onboardedUser);

    return {
      user: await this.classMapper.mapAsync(
        onboardedUser,
        User,
        UserProfileDto,
      ),
      accessToken: token.accessToken,
    };
  }

  /**
   * Signin method.
   * This method is used to sign in a user.
   * It checks if the user exists and if the password matches.
   * @param email - User email.
   * @param password - User password.
   */
  async signin(email: string, password: string) {
    // Get user information
    const user = await this.userService.findByEmail(email);

    // Check if user exists
    if (!user) {
      this.logger.error('User not found');
      throw new NotFoundException(`User with email ${email} not found`);
    }

    if (!CoreUtils.compare(password, user.password)) {
      this.logger.error('Password does not match');
      throw new BadRequestException('Password does not match');
    }

    if (!user.isVerified) {
      this.logger.error('User is not verified');
      throw new ForbiddenException('User is not verified');
    }

    if (user.status === EntityStatus.INACTIVE) {
      this.logger.error('User is inactive');
      throw new ForbiddenException('User is inactive');
    }

    const token = await this.generateTokens(user.id, user.email);
    await this.updateRefreshToken(user.id, token.refreshToken);

    return {
      accessToken: token.accessToken,
      refreshToken: token.refreshToken,
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phoneNumber: user.phoneNumber,
        hasDefaultPassword: user.hasDefaultPassword,
        roleId: user.role?.id ?? null,
        departmentId: user.department?.id ?? null,
      },
    };
  }

  /**
   * Log user out by removing the refresh token from the database for the user with the given id (userId)
   * @param userId - The id of the user.
   */
  async signOut(userId: number): Promise<boolean> {
    const user = await this.userService.findByPk(userId);
    if (!user) throw new NotFoundException('User not found');
    user.refreshToken = null;
    await this.userService.update(user);

    return true;
  }

  /**
   * Validate user email
   * @param email
   */
  async validate(email: string): Promise<User> {
    const existing: User = await this.userService.findByEmail(email);
    if (!existing) {
      throw Error('Email not found.');
    }
    return existing;
  }

  /**
   * Recover user password
   * @returns Promise<string>
   * @param email
   */
  async recoverPassword(email: string): Promise<string> {
    try {
      // validate email
      const user = await this.validate(email);

      if (!user || user.status === EntityStatus.INACTIVE) {
        return CoreUtils.buildFailureResponse(
          'Not found.',
          'User not found.',
          HttpStatus.NOT_FOUND,
        );
      } else {
        await this.userService.update(user);
        return user.password;
        // return await this.mailService.passwordResetMail(user);
      }
    } catch (e) {
      //return 404
      this.logger.debug(e.message);
      console.log(e);
      throw new Error('Password recovery failed');
    }
  }

  async verifyUser(verifyUserDto: VerifyUserDto) {
    const user = await this.userService.findByEmail(verifyUserDto.email);

    if (!user) {
      throw new NotFoundException(
        `User with email ${verifyUserDto.email} not found`,
      );
    }

    if (user.isVerified) {
      throw new ConflictException('User is already verified');
    }

    if (user.token !== verifyUserDto.token) {
      throw new ConflictException('Token is invalid');
    }

    if (CoreUtils.hasExpired(user.expiresAt)) {
      throw new ConflictException('Token has expired');
    }

    const { accessToken, refreshToken } = await this.generateTokens(
      user.id,
      user.email,
    );

    user.isVerified = true;
    user.status = EntityStatus.ACTIVE;
    user.refreshToken = CoreUtils.hash(refreshToken);

    await this.userService.update(user);

    return { accessToken, refreshToken };
  }

  async changePassword(changePasswordDto: ChangePasswordDto, userId: number) {
    const user = await this.userService.findByPk(userId);

    const { oldPassword, newPassword } = changePasswordDto;

    if (!CoreUtils.compare(oldPassword, user.password)) {
      throw new BadRequestException('Password is incorrect');
    }

    user.password = CoreUtils.hash(newPassword);
    user.hasDefaultPassword = false;
    await this.userService.update(user);
  }

  /**
   * Reset user password
   * @param password
   * @param user
   */
  async resetPassword(password: string, user: User): Promise<User> {
    // reset password
    user.password = bcrypt.hashSync(password, 10);
    if (user.status === EntityStatus.INACTIVE) {
      user.status = EntityStatus.ACTIVE;
    }
    return user;
  }

  async forgotPassword(email: string): Promise<void> {
    try {
      const user = await this.userService.findByEmail(email);
      console.log('user', user);
      if (!user) {
        throw new NotFoundException(`user with email ${email} not found`);
      } else {
        const tokens = await this.generateTokens(user.id, user.email);
        // passwordReset = this.passwordResetRepository.create({
        //   token: tokens.refreshToken,
        //   user,
        // });
        const resetPasswordUrl =
          await this.userService.generatePasswordResetUrl(tokens.refreshToken);
        // await this.mailService.sendSmtpMail(
        //   `${email}`,
        //   'Password Reset',
        //   `
        //   <p>Click the link below to reset your password</p>
        //   <a href="${resetPasswordUrl}/">Reset Password</a>
        //   `,
        // );
        const requesterSubject = 'Password Reset';
        const requesterMessage = `Click the link below to reset your password.\n ${resetPasswordUrl} God bless you!`;

        await this.mailService.sendSmtpMail(
          email,
          requesterSubject,
          requesterMessage,
        );
      }
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }

  async updateRefreshToken(
    userId: number,
    refreshToken: string,
  ): Promise<void> {
    const user = await this.userService.findByPk(userId);
    if (!user) throw new ForbiddenException('User not found');

    user.refreshToken = CoreUtils.hash(refreshToken);
    await this.userService.update(user);
  }

  /**
   * This method is used to create access and refresh tokens for a user.
   * @param userId - The id of the user to create access token for.
   * @param email - The email of the user to create access token for.
   * @returns Promise<string> - The access token.
   */
  async generateTokens(userId: number, email: string): Promise<Tokens> {
    const payload: JwtPayload = { sub: userId, email };
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('keys.secret'),
        expiresIn: '7d',
      }),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('keys.secret'),
        expiresIn: '30d',
      }),
    ]);

    return {
      accessToken,
      refreshToken,
    };
  }

  /**
   * This method is used to refresh the access token of a user.
   * @param userId - The id of the user to create refresh token for.
   * @param refreshToken - The refresh token to create.
   */
  async refreshTokens(userId: number, refreshToken: string): Promise<Tokens> {
    const user: User = await this.userService.findByPk(userId);
    if (!user || !user.refreshToken)
      throw new UnauthorizedException('Access Denied');

    const rtMatches = CoreUtils.compare(refreshToken, user.refreshToken);
    if (!rtMatches) throw new UnauthorizedException('Invalid refresh token');

    const tokens = await this.generateTokens(userId, user.email);
    await this.updateRefreshToken(userId, tokens.refreshToken);
    return tokens;
  }
}
