import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateItemAuditTable1747339793048 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('item_audit', [
      new TableColumn({
        name: 'audit_collected',
        type: 'boolean',
        default: false,
      }),
      new TableColumn({
        name: 'audit_collected_date',
        type: 'timestamp',
        isNullable: true,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('item_audit', 'audit_collected');
    await queryRunner.dropColumn('item_audit', 'audit_collected_date');
  }
}
