import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateUser1721846205810 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.changeColumn(
      'user',
      'expires_at',
      new TableColumn({
        name: 'expires_at',
        type: 'bigint',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.changeColumn(
      'user',
      'expires_at',
      new TableColumn({
        name: 'expires_at',
        type: 'int',
        isNullable: true,
      }),
    );
  }
}
