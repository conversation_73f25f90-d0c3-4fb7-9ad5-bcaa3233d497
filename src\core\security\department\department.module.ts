import { Module } from '@nestjs/common';
import { DepartmentService } from './department.service';
import { DepartmentController } from './department.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Department } from './entities/department.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { DepartmentValidator } from './department.validator';
import { DepartmentProfile } from './department.profile';
import { AuthorizationModule } from '@core/security/authorization/authorization.module';
import { LoggerModule } from '@common/logger/logger.module';

@Module({
  controllers: [DepartmentController],
  exports: [DepartmentService, DepartmentValidator],
  imports: [
    TypeOrmModule.forFeature([Department]),
    AutomapperModule,
    AuthorizationModule,
    LoggerModule,
  ],
  providers: [DepartmentService, DepartmentValidator, DepartmentProfile],
})
export class DepartmentModule {}
