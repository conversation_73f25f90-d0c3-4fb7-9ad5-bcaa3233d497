import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
    }),
  );
  app.enableCors();
  app.setGlobalPrefix('api/');
  app.useGlobalFilters(new HttpExceptionFilter());

  // URI versioning
  app.enableVersioning({
    type: VersioningType.URI,
  });

  // Get required services
  const configService = app.get(ConfigService);

  // Swagger configuration
  const swaggerConfig = new DocumentBuilder()
    //.addBearerAuth()
    .setTitle('EGFM Logistic Inventory Management System APIs')
    .setDescription(
      'This is the API documentation for the EGFM Logistic Inventory Management System.',
    )
    .setVersion('1.0')
    .addTag('api')
    .build();
  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup('api-docs', app, document);

  // Serve Swagger JSON at a specific endpoint (optional customization)
  app.getHttpAdapter().get('/api-docs-json', (req, res) => {
    res.json(document);
  });

  console.log(
    `Application running on port ${configService.get<string>('port')}`,
  );

  await app.listen(configService.get<string>('port'));
}

bootstrap();
