import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateItem1721820699806 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'item',
      new TableColumn({
        name: 'available_quantity',
        type: 'bigint',
        default: 0,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('item', 'available_quantity');
  }
}
