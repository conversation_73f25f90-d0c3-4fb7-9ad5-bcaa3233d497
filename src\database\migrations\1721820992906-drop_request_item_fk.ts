import {
  MigrationInterface,
  QueryRunner,
  <PERSON><PERSON><PERSON>umn,
  TableForeign<PERSON>ey,
} from 'typeorm';

export class DropRequestItemFk1721820992906 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('item');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('request_id') !== -1,
    );
    await queryRunner.dropForeignKey('item', foreignKey);
    await queryRunner.dropColumn('item', 'request_id');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'item',
      new TableColumn({
        name: 'request_id',
        type: 'bigint',
      }),
    );

    await queryRunner.createForeignKey(
      'item',
      new TableForeignKey({
        columnNames: ['request_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'request',
      }),
    );
  }
}
