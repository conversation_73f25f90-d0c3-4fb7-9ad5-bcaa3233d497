import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateRequestAuditTable1747338338116
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('request_audit', [
      new TableColumn({
        name: 'collected',
        type: 'boolean',
        default: false,
      }),
      new TableColumn({
        name: 'collected_date',
        type: 'timestamp',
        isNullable: true,
      }),
      new TableColumn({
        name: 'collected_by',
        type: 'varchar',
        isNullable: true,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('request_audit', 'collected');
    await queryRunner.dropColumn('request_audit', 'collected_date');
    await queryRunner.dropColumn('request_audit', 'collected_by');
  }
}
