import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityStatus } from '@common/entities/base.entity';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { PaginationQueryParams } from '@common/types';
import { User } from '@core/security/user/entities/user.entity';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  IPaginationOptions,
  paginate,
  Pagination,
} from 'nestjs-typeorm-paginate';
import { ILike, Repository } from 'typeorm';
import { CreateGeneratorLogDto } from './dto/create-generator-log.dto';
import { GeneratorLogDto } from './dto/generator-log.dto';
import { UpdateGeneratorLogDto } from './dto/update-generator-log.dto';
import { GeneratorLog } from './entities/generator-log.entity';
import { GeneratorLogValidator } from './generator-log.validator';

@Injectable()
export class GeneratorLogService {
  constructor(
    @InjectRepository(GeneratorLog)
    private readonly generatorRepository: Repository<GeneratorLog>,
    private readonly generatorValidator: GeneratorLogValidator,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  async create(createGeneratorLogDto: CreateGeneratorLogDto, user: User) {
    const generatorLog = await this.classMapper.mapAsync(
      createGeneratorLogDto,
      CreateGeneratorLogDto,
      GeneratorLog,
    );
    await this.generatorValidator.validate(generatorLog, DatabaseAction.CREATE);
    generatorLog.createdBy = `${user.firstName} ${user.lastName}`;
    await this.generatorRepository.save(generatorLog);
  }

  async updateGeneratorLog(
    id: number,
    updateGeneratorDto: UpdateGeneratorLogDto,
    user: User,
  ) {
    const generatorLog = await this.findByPk(id);
    if (!generatorLog) {
      throw new NotFoundException('Generator Log not found');
    }
    await this.classMapper.mutateAsync(
      updateGeneratorDto,
      generatorLog,
      GeneratorLogDto,
      GeneratorLog,
    );
    generatorLog.updatedBy = `${user.firstName} ${user.lastName}`;
    await this.update(generatorLog, user);
  }

  async getGeneratorLog(id: number) {
    const generatorLog = await this.findByPk(id);
    if (!generatorLog) {
      throw new NotFoundException('Generator Log not found');
    }

    return await this.classMapper.mapAsync(
      generatorLog,
      GeneratorLog,
      GeneratorLogDto,
    );
  }

  async update(generatorLog: GeneratorLog, user: User) {
    await this.generatorValidator.validate(generatorLog, DatabaseAction.UPDATE);
    generatorLog.createdBy = `${user.firstName} ${user.lastName}`;
    await this.generatorRepository.save(generatorLog);
  }

  async getPaginatedGeneratorLogs(
    params: PaginationQueryParams,
    route: string,
  ) {
    const { page, limit, search, filter } = params;
    const where = {};
    if (search) {
      where['nameOfMeeting'] = ILike(`%${search}%`);
    }
    if (filter) {
      where['createdBy'] = filter;
      where['createdDate'] = filter;
    }

    const options = {
      page,
      limit: limit > 100 ? 100 : limit, // limit the pagination to 100
      route,
    };

    const pagination = await this.paginate(options, where);

    const dtoList = await this.classMapper.mapArrayAsync(
      pagination.items,
      GeneratorLog,
      GeneratorLogDto,
    );

    return new Pagination(dtoList, pagination.meta, pagination.links);
  }

  async findAll(): Promise<Array<GeneratorLog>> {
    return this.generatorRepository.find();
  }

  async findByPk(id: number): Promise<GeneratorLog> {
    return this.generatorRepository.findOneBy({ id });
  }

  async findFirstBy(where: any): Promise<GeneratorLog | null> {
    return this.generatorRepository.findOneBy(where);
  }

  async delete(id: number): Promise<void> {
    await this.generatorRepository.delete(id);
  }

  async activate(ids: Array<number>, user: User): Promise<void> {
    ids.map(async (id) => {
      const generatorLog: GeneratorLog = await this.findByPk(id);
      generatorLog.status = EntityStatus.ACTIVE;
      await this.update(generatorLog, user);
    });
  }

  async deactivate(ids: Array<number>, user: User): Promise<void> {
    ids.map(async (id) => {
      const generatorLog: GeneratorLog = await this.findByPk(id);
      generatorLog.status = EntityStatus.INACTIVE;
      await this.update(generatorLog, user);
    });
  }

  async paginate(
    options: IPaginationOptions,
    where?: any,
  ): Promise<Pagination<GeneratorLog>> {
    if (where && Object.keys(where).length > 0) {
      return paginate<GeneratorLog>(this.generatorRepository, options, {
        where,
        order: {
          createdAt: 'DESC',
        },
      });
    }
    return paginate<GeneratorLog>(this.generatorRepository, options);
  }

  // server side search without pagination
  async search(query: string): Promise<Array<GeneratorLog>> {
    return await this.generatorRepository.find({
      where: [{ nameOfMeeting: ILike(`%${query}%`) }],
    });
  }

  // server side search with pagination
  async paginatedSearch(
    query: string,
    page = 1,
    limit = 10,
  ): Promise<Array<GeneratorLog>> {
    const [results] = await this.generatorRepository.findAndCount({
      where: [{ nameOfMeeting: ILike(`%${query}%`) }],
      skip: (page - 1) * limit,
      take: limit,
    });
    return results;
  }
}
