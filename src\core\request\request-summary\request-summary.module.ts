import { Module } from '@nestjs/common';
import { RequestSummaryMapperService } from './request-summary.mapper.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RequestSummary } from './entities/request-summary.entity';
import { AutomapperModule } from '@automapper/nestjs';

@Module({
  controllers: [],
  exports: [RequestSummaryMapperService],
  imports: [TypeOrmModule.forFeature([RequestSummary]), AutomapperModule],
  providers: [RequestSummaryMapperService],
})
export class RequestSummaryModule {}
