import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, Mapper, MappingProfile } from '@automapper/core';
import { RequestSummary } from './entities/request-summary.entity';
import { RequestSummaryDto } from './dto/request-summary.dto';

@Injectable()
export class RequestSummaryMapperService extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, RequestSummary, RequestSummaryDto);
      createMap(mapper, RequestSummaryDto, RequestSummary);
    };
  }
}
