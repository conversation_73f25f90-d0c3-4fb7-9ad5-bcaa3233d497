export interface IEntity<T> {
  create(entity: T): Promise<T>;

  delete(id: number): Promise<void>;

  find(options: object): Promise<Array<T>>;

  findAll(): Promise<Array<T>>;

  findByPk(id: number): Promise<T>;

  update(entity: T): Promise<T>;

  insert(entity: T): Promise<void>;

  findAndCount(options: object): Promise<[Array<T>, number]>;

  count(options: object): Promise<number>;

  getEntityRepository(): any;
}
