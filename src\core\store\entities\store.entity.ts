import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneTo<PERSON>ne } from 'typeorm';
import { AbstractEntity } from '@common/entities/base.entity';
import { AutoMap } from '@automapper/classes';
import { Address } from '@core/security/address/entities/address.entity';

@Entity({ name: 'store' })
export class Store extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'name', type: 'varchar', length: 100, nullable: false })
  name: string;

  @AutoMap()
  @OneToOne(() => Address, (address) => address.store, {
    orphanedRowAction: 'delete',
    eager: true,
  })
  @JoinColumn({ name: 'address_id', referencedColumnName: 'id' })
  location: Address;
}
