import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber } from 'class-validator';
import { Condition } from '@common/enums/condition.enum';

export class DepartmentItemDto {
  @AutoMap(() => Number)
  @ApiProperty()
  id?: number;

  @AutoMap(() => Number)
  @ApiProperty()
  storeId: number;

  @AutoMap()
  @ApiProperty()
  storeName?: string;

  @AutoMap()
  @ApiProperty()
  name: string;

  @AutoMap(() => Number)
  @ApiProperty()
  @IsNumber()
  availableQuantity: number;

  @AutoMap(() => Boolean)
  @ApiProperty({
    type: Boolean,
    default: false,
  })
  fragile: boolean;

  @AutoMap(() => String)
  @ApiProperty({
    description: 'Possible conditions of an item',
    enum: Object.values(Condition),
    name: 'condition',
    type: 'string',
    default: Condition.NOT_SPECIFIED,
  })
  condition: Condition;
}
