import { MigrationInterface, QueryRunner, TableUnique } from 'typeorm';

export class UpdateItemTable1723635401466 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createUniqueConstraint(
      'item',
      new TableUnique({
        columnNames: ['name', 'serial_number'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropUniqueConstraint('item', 'name');
    await queryRunner.dropUniqueConstraint('item', 'serial_number');
  }
}
