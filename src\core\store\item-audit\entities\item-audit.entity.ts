import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { AbstractEntity } from '@common/entities/base.entity';
import { Condition } from '@common/enums/condition.enum';
import { Request } from '@core/request/entities/request.entity';

@Entity({ name: 'item_audit' })
export class ItemAudit extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'store_id', type: 'bigint' })
  storeId: number;

  @AutoMap()
  @Column({ name: 'store_name' })
  storeName: string;

  @AutoMap()
  @Column({ name: 'item_id' })
  itemId: number;

  @AutoMap()
  @Column({ name: 'item_name' })
  itemName: string;

  @AutoMap()
  @Column({ name: 'quantity_leased_out', type: 'bigint', default: 0 })
  quantityLeased: number;

  @AutoMap()
  @Column({ name: 'leased_out_date', type: 'timestamp' })
  leasedDate: Date;

  @AutoMap(() => String)
  @Column({
    name: 'condition_before_leased_out',
    type: 'enum',
    enum: Condition,
    default: Condition.NOT_SPECIFIED,
  })
  conditionBeforeLease: Condition;

  @AutoMap()
  @Column({ name: 'quantity_released', type: 'bigint', default: 0 })
  quantityReleased: number;

  @AutoMap()
  @Column({ name: 'released_date', type: 'timestamp' })
  releasedDate: Date;

  @AutoMap()
  @Column({ name: 'quantity_returned', type: 'bigint', default: 0 })
  quantityReturned: number;

  @AutoMap()
  @Column({ name: 'returned_date', type: 'timestamp' })
  returnedDate: Date;

  @AutoMap(() => String)
  @Column({
    name: 'condition_after_leased_out',
    type: 'enum',
    enum: Condition,
    default: Condition.NOT_SPECIFIED,
  })
  conditionAfterReturned: Condition;

  @AutoMap()
  @Column({ name: 'audit_completed', type: 'boolean', default: false })
  audit_completed: boolean;

  @AutoMap()
  @Column({ name: 'audit_completed_date', type: 'timestamp' })
  auditCompletionDate: Date;

  @AutoMap()
  @Column({ name: 'audit_collected', type: 'boolean', default: false })
  audit_collected: boolean;

  @AutoMap()
  @Column({ name: 'audit_collected_date', type: 'timestamp' })
  auditCollectionDate: Date;

  @AutoMap()
  @ManyToOne(() => Request, {
    orphanedRowAction: 'delete',
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'request_id', referencedColumnName: 'id' })
  request: Request;
}
