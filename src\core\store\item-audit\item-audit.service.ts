import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ItemAudit } from './entities/item-audit.entity';
import { ILike, Repository } from 'typeorm';
import { ItemAuditValidator } from './item-audit.validator';
import {
  IPaginationOptions,
  paginate,
  Pagination,
} from 'nestjs-typeorm-paginate';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { EntityStatus } from '@common/entities/base.entity';

@Injectable()
export class ItemAuditService {
  constructor(
    @InjectRepository(ItemAudit)
    private readonly itemAuditRepository: Repository<ItemAudit>,
    private readonly itemAuditValidator: ItemAuditValidator,
  ) {}

  async create(item: ItemAudit): Promise<ItemAudit | undefined> {
    await this.itemAuditValidator.validate(item, DatabaseAction.CREATE);
    return this.itemAuditRepository.save(item);
  }

  async update(item: ItemAudit): Promise<ItemAudit | undefined> {
    await this.itemAuditValidator.validate(item, DatabaseAction.UPDATE);
    return this.itemAuditRepository.save(item);
  }

  async findAll(): Promise<Array<ItemAudit>> {
    return this.itemAuditRepository.find();
  }

  async findByPk(id: number): Promise<ItemAudit> {
    return this.itemAuditRepository.findOneBy({ id });
  }

  async findFirstBy(where: any): Promise<ItemAudit> {
    return this.itemAuditRepository.findOneBy(where);
  }

  async delete(id: number): Promise<void> {
    await this.itemAuditRepository.delete(id);
  }

  async activate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const item: ItemAudit = await this.findByPk(id);
      item.status = EntityStatus.ACTIVE;
      await this.update(item);
    });
  }

  async deactivate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const item: ItemAudit = await this.findByPk(id);
      item.status = EntityStatus.INACTIVE;
      await this.update(item);
    });
  }

  async paginate(
    options: IPaginationOptions,
    where?: any,
  ): Promise<Pagination<ItemAudit>> {
    if (where && Object.keys(where).length > 0) {
      return paginate<ItemAudit>(this.itemAuditRepository, options, {
        where,
        order: {
          createdAt: 'DESC',
        },
      });
    }
    return paginate<ItemAudit>(this.itemAuditRepository, options);
  }

  // server side search without pagination
  async search(query: string): Promise<Array<ItemAudit>> {
    return await this.itemAuditRepository.find({
      where: [{ status: ILike(`%${query}%`) }],
    });
  }

  // server side search with pagination
  async paginatedSearch(
    query: string,
    page = 1,
    limit = 10,
  ): Promise<Array<ItemAudit>> {
    const [results] = await this.itemAuditRepository.findAndCount({
      where: [{ status: ILike(`%${query}%`) }],
      skip: (page - 1) * limit,
      take: limit,
    });
    return results;
  }

  async findAllItemAuditByRequestId(
    requestId: number,
  ): Promise<Array<ItemAudit>> {
    return this.itemAuditRepository.find({
      where: { request: { id: requestId } },
    });
  }
}
