import {
  MigrationInterface,
  QueryRunner,
  <PERSON>C<PERSON>umn,
  TableForeignKey,
} from 'typeorm';

export class CreateStoreAddressFk1720828861356 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add a column to the store table to reference the address table.
    await queryRunner.addColumn(
      'store',
      new TableColumn({
        name: 'address_id',
        type: 'bigint',
        isNullable: true,
      }),
    );

    // Add a foreign key to the address table.
    await queryRunner.createForeignKey(
      'store',
      new TableForeignKey({
        columnNames: ['address_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'address',
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop FK, store table, and its index
    const storeTable = await queryRunner.getTable('store');
    const fk = storeTable.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('address_id') !== -1,
    );
    await queryRunner.dropForeignKey('store', fk);
    await queryRunner.dropColumn('store', 'address_id');
  }
}
