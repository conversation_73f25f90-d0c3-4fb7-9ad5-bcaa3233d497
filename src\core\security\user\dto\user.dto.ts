import { EntityDto } from '../../../../common/dto/base.dto';
import { AutoMap } from '@automapper/classes';
import { IsEmail } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { AddressDto } from '../../address/dto/address.dto';
import { Gender } from '../../../../common/enums/gender.enum';

export class UserDto extends EntityDto {
  @AutoMap()
  @IsEmail()
  @ApiProperty()
  email: string;

  @AutoMap()
  @ApiProperty()
  firstName?: string;

  @AutoMap()
  @ApiProperty()
  lastName?: string;

  @AutoMap()
  @ApiProperty()
  phoneNumber?: string;

  @AutoMap()
  @ApiProperty()
  password: string;

  @AutoMap()
  @ApiProperty({
    type: 'string',
    enum: Object.values(Gender),
    name: 'gender',
    description: 'Possible genders',
  })
  gender: Gender;

  @AutoMap()
  @ApiProperty()
  department: string;

  @AutoMap()
  @ApiProperty()
  role: string;

  @AutoMap(() => AddressDto)
  @ApiProperty()
  address?: AddressDto;
}
