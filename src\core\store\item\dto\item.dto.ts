import { EntityDto } from '@common/dto/base.dto';
import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Condition } from '@common/enums/condition.enum';
import { StoreDto } from '@core/store/dto/store.dto';
import { DepartmentDto } from '@core/security/department/dto/department.dto';

export class ItemDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  name: string;

  @AutoMap(() => Number)
  @ApiProperty()
  actualQuantity: number;

  @AutoMap(() => Number)
  @ApiProperty()
  availableQuantity: number;

  @AutoMap(() => StoreDto)
  @ApiProperty()
  store: StoreDto;

  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
  })
  fragile: boolean;

  @AutoMap(() => String)
  @ApiProperty({
    description: 'Possible conditions of an item',
    enum: Object.values(Condition),
    name: 'condition',
    type: 'string',
    default: Condition.NOT_SPECIFIED,
  })
  condition: Condition;

  @AutoMap(() => DepartmentDto)
  @ApiProperty()
  department: DepartmentDto;
}
