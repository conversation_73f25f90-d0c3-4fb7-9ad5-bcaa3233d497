import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { createMap, Mapper, MappingProfile } from '@automapper/core';
import { RequestAudit } from './entities/request-audit.entity';
import { RequestAuditDto } from './dto/request-audit.dto';

@Injectable()
export class RequestAuditProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, RequestAudit, RequestAuditDto);
      createMap(mapper, RequestAuditDto, RequestAudit);
    };
  }
}
