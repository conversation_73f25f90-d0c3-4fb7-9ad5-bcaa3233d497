import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateItemAudit1747609223091 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('item_audit', [
      new TableColumn({
        name: 'quantity_released',
        type: 'bigint',
        default: 0,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('item_audit', 'quantity_released');
  }
}
