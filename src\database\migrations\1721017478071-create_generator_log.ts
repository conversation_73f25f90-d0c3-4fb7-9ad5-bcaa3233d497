import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateGeneratorLog1721017478071 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'generator_log',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
          { name: 'name_of_meeting', type: 'varchar' },
          { name: 'generator_type', type: 'varchar' },
          { name: 'meeting_location', type: 'varchar' },
          { name: 'on_time', type: 'timestamp' },
          { name: 'off_time', type: 'timestamp', isNullable: true },
          { name: 'hours_used', type: 'timestamp' },
          { name: 'engine_start_hours', type: 'varchar' },
          { name: 'engine_off_hours', type: 'varchar' },
          { name: 'diesel_level_on', type: 'varchar' },
          { name: 'diesel_level_off', type: 'varchar' },
          { name: 'last_service_hour', type: 'timestamp' },
          { name: 'next_service_hour', type: 'timestamp', isNullable: true },
          { name: 'due_for_service', type: 'boolean', default: false },
          {
            name: 'oil_filter_due_for_replacement',
            type: 'boolean',
            default: false,
          },
          {
            name: 'last_oil_filter_replacement',
            type: 'timestamp',
            isNullable: true,
          },
          { name: 'fault_detected', type: 'boolean', default: false },
          { name: 'fault_description', type: 'varchar', isNullable: true },
          { name: 'personnel_name', type: 'varchar', isNullable: true },
          { name: 'remark', type: 'text', isNullable: true },
        ],
      }),
      true,
    );

    // Add an index to the table.
    await queryRunner.createIndex(
      'generator_log',
      new TableIndex({
        name: 'IDX_GEN_LOG_MEETING_NAME',
        columnNames: ['name_of_meeting'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop index and table
    await queryRunner.dropIndex('generator_log', 'IDX_GEN_LOG_MEETING_NAME');
    await queryRunner.dropTable('generator_log');
  }
}
