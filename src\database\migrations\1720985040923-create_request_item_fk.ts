import {
  MigrationInterface,
  QueryRunner,
  <PERSON>Column,
  TableForeign<PERSON>ey,
} from 'typeorm';

export class CreateRequestItemFk1720985040923 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'item',
      new TableColumn({
        name: 'request_id',
        type: 'bigint',
      }),
    );

    await queryRunner.createForeignKey(
      'item',
      new TableForeignKey({
        columnNames: ['request_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'request',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('item');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('request_id') !== -1,
    );
    await queryRunner.dropForeignKey('request_id', foreignKey);
    await queryRunner.dropColumn('item', 'request_id');
  }
}
