import {
  MigrationInterface,
  QueryRunner,
  <PERSON>C<PERSON>umn,
  TableForeign<PERSON>ey,
} from 'typeorm';

export class CreateRequestItemAuditFk1720986860462
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'item_audit',
      new TableColumn({
        name: 'request_id',
        type: 'bigint',
      }),
    );

    await queryRunner.createForeignKey(
      'item_audit',
      new TableForeignKey({
        columnNames: ['request_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'request',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('item_audit');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('request_id') !== -1,
    );
    await queryRunner.dropForeignKey('request_id', foreignKey);
    await queryRunner.dropColumn('item_audit', 'request_id');
  }
}
