import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateRequestSummary1720948942630 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    /**
     * Create request-summary Table.
     */
    await queryRunner.createTable(
      new Table({
        name: 'request_summary',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'request_status',
            type: 'varchar',
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    /**
     * Create Index
     */
    // Add an index to the user table.
    await queryRunner.createIndex(
      'request_summary',
      new TableIndex({
        name: 'IDX_REQUEST_STATUS',
        columnNames: ['request_status'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    //Drop user table and its index
    await queryRunner.dropIndex('request_summary', 'IDX_REQUEST_STATUS');
    await queryRunner.dropTable('request_summary');
  }
}
