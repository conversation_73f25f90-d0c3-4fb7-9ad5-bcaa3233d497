import { IPaginationOptions, Pagination } from 'nestjs-typeorm-paginate';

export interface IService<T> {
  activate(ids: Array<number>): Promise<void>;

  deactivate(ids: Array<number>): Promise<void>;

  paginate(
    options: IPaginationOptions,
    where?: any,
    ...args: any[]
  ): Promise<Pagination<T>>;

  paginatedSearch(
    query: string,
    page: number,
    limit: number,
  ): Promise<Array<T>>;

  search(query: string): Promise<Array<T>>;
}
