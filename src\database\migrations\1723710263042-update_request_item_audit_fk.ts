import { MigrationInterface, QueryRunner, TableForeign<PERSON>ey } from 'typeorm';

export class UpdateRequestItemAuditFk1723710263042
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the existing foreign key
    const table = await queryRunner.getTable('item_audit');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('request_id') !== -1,
    );

    if (foreignKey) {
      await queryRunner.dropForeignKey('item_audit', foreignKey);
    }

    // Create the new foreign key with onDelete: "CASCADE"
    await queryRunner.createForeignKey(
      'item_audit',
      new TableForeignKey({
        columnNames: ['request_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'request',
        onDelete: 'CASCADE', // Add onDelete: CASCADE
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the cascade foreign key
    const table = await queryRunner.getTable('item_audit');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('request_id') !== -1,
    );

    if (foreignKey) {
      await queryRunner.dropForeignKey('item_audit', foreignKey);
    }

    // Re-create the original foreign key without onDelete: "CASCADE"
    await queryRunner.createForeignKey(
      'item_audit',
      new TableForeignKey({
        columnNames: ['request_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'request',
      }),
    );
  }
}
