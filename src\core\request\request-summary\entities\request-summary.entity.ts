import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { RequestAudit } from '../../request-audit/entities/request-audit.entity';
import { AbstractEntity } from '@common/entities/base.entity';
import { RequestStatus } from '@common/enums/request-status.enum';
import { Request } from '@core/request/entities/request.entity';

@Entity({ name: 'request_summary' })
export class RequestSummary extends AbstractEntity {
  @AutoMap()
  @OneToOne(() => Request, (request) => request.summary, {})
  @JoinColumn({ name: 'request_id', referencedColumnName: 'id' })
  request: Request;

  @AutoMap(() => String)
  @Column({
    default: RequestStatus.DEFAULT,
    enum: RequestStatus,
    name: 'request_status',
    type: 'enum',
  })
  requestStatus: RequestStatus;

  @AutoMap()
  @OneToOne(() => RequestAudit, (audit) => audit.requestSummary, {
    cascade: true,
    eager: true,
  })
  audit: RequestAudit;
}
