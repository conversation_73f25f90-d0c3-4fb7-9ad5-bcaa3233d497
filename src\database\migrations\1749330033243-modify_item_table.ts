import {
  MigrationInterface,
  QueryRunner,
  TableC<PERSON>umn,
  TableForeign<PERSON>ey,
} from 'typeorm';

export class ModifyItemTable1749330033243 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop store_id FK
    const itemTable = await queryRunner.getTable('item');
    const fk = itemTable.foreignKeys.find(
      (key) => key.columnNames.indexOf('store_id') !== -1,
    );
    await queryRunner.dropForeignKey('item', fk);
    await queryRunner.dropColumns('item', [
      'store_id',
      'serial_number',
      'condition',
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('item', [
      new TableColumn({
        name: 'store_id',
        type: 'bigint',
      }),
      new TableColumn({
        name: 'serial_number',
        type: 'varchar',
        isNullable: true,
      }),
      new TableColumn({
        name: 'condition',
        type: 'varchar',
      }),
    ]);

    await queryRunner.createForeignKey(
      'item',
      new TableForeignKey({
        columnNames: ['store_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'store',
      }),
    );
  }
}
