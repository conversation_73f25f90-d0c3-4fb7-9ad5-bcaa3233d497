import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateItem1721822921559 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('item', 'department');

    await queryRunner.addColumn(
      'item',
      new TableColumn({
        name: 'department_id',
        type: 'bigint',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('item', 'department_id');

    await queryRunner.addColumn(
      'item',
      new TableColumn({
        name: 'department',
        type: 'varchar',
      }),
    );
  }
}
