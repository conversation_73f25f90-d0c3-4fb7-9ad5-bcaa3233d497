import {
  MigrationInterface,
  QueryRunner,
  <PERSON><PERSON><PERSON>umn,
  TableForeignKey,
} from 'typeorm';

export class CreateRequestSummaryFk1720949038531 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add a column to the request-summary table to reference the request table.
    await queryRunner.addColumn(
      'request_summary',
      new TableColumn({
        name: 'request_id',
        type: 'bigint',
        isNullable: true,
      }),
    );

    // Add a foreign key to the request-summary table.
    await queryRunner.createForeignKey(
      'request_summary',
      new TableForeignKey({
        columnNames: ['request_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'request',
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop FK, request-summary table, and its index
    const requestSummaryTable = await queryRunner.getTable('request_summary');
    const fk = requestSummaryTable.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('request_id') !== -1,
    );
    await queryRunner.dropForeignKey('request_summary', fk);
    await queryRunner.dropColumn('request_summary', 'request_id');
  }
}
