import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityStatus } from '@common/entities/base.entity';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { PaginationQueryParams } from '@common/types';
import { User } from '@core/security/user/entities/user.entity';
import { StoreDto } from '@core/store/dto/store.dto';
import { StoreService } from '@core/store/store.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as _ from 'lodash';
import {
  IPaginationOptions,
  paginate,
  Pagination,
} from 'nestjs-typeorm-paginate';
import { ILike, Repository } from 'typeorm';
import { CreateItemDto } from './dto/create-item.dto';
import { DepartmentItemDto } from './dto/department-item.dto';
import { ItemDto } from './dto/item.dto';
import { Item } from './entities/item.entity';
import { ItemValidatorService } from './item.validator.service';
import { StoreValidator } from '@core/store/store.validator';
import { Store } from '@core/store/entities/store.entity';
import { DepartmentService } from '@core/security/department/department.service';
import { Department } from '@core/security/department/entities/department.entity';
import { DepartmentDto } from '@core/security/department/dto/department.dto';
import { DepartmentValidator } from '@core/security/department/department.validator';
import { CoreUtils } from '@common/utils/core.utils';
import { Condition } from '@common/enums/condition.enum';

@Injectable()
export class ItemService {
  constructor(
    @InjectRepository(Item) private readonly itemRepository: Repository<Item>,
    private readonly itemValidator: ItemValidatorService,
    private readonly storeValidator: StoreValidator,
    private readonly departmentValidator: DepartmentValidator,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly storeService: StoreService,
    private readonly departmentService: DepartmentService,
  ) {}

  private async validateDependencies(item: Item): Promise<void> {
    if (
      !(await this.departmentValidator.isDepartmentExists(item.departmentId))
    ) {
      throw new NotFoundException(
        `Department with id ${item.departmentId} does not exist`,
      );
    }
    if (!(await this.storeValidator.isStoreExists(item.storeId))) {
      throw new NotFoundException(
        `Store with id ${item.storeId} does not exist`,
      );
    }
  }

  async create(createItemDto: CreateItemDto, user: User) {
    const item = await this.classMapper.mapAsync(
      createItemDto,
      CreateItemDto,
      Item,
    );

    item.createdBy = `${user.firstName} ${user.lastName}`;

    // At the initial stage, the available quantity is the same as the quantity
    item.availableQuantity = item.actualQuantity;

    // Get department information for serial number generation
    const department = await this.departmentService.findByPk(item.departmentId);
    if (!department) {
      throw new NotFoundException('Department not found');
    }

    // TODO: Based on the available quantity, create new item records in the database new serial number for each item.

    // Generate serial number
    const count = await this.itemRepository.count({
      where: { departmentId: item.departmentId },
    });
    item.serialNumber = CoreUtils.generateSerialNumber(
      department.name,
      item.name,
      'EGFM',
      count,
    );

    await this.itemValidator.validate(item, DatabaseAction.CREATE);
    await this.validateDependencies(item);
    await this.itemRepository.save(item);
  }

  async createMany(createItemDtos: CreateItemDto[], user: User) {
    const items = await this.classMapper.mapArrayAsync(
      createItemDtos,
      CreateItemDto,
      Item,
    );

    // Group items by department for serial number generation
    const itemsByDepartment = _.groupBy(items, 'departmentId');

    // Generate serial numbers for each department's items
    for (const [departmentId, departmentItems] of Object.entries(
      itemsByDepartment,
    )) {
      const department = await this.departmentService.findByPk(
        Number(departmentId),
      );
      if (!department) {
        throw new NotFoundException(
          `Department with id ${departmentId} not found`,
        );
      }

      const count = await this.itemRepository.count({
        where: { departmentId: Number(departmentId) },
      });

      (departmentItems as Item[]).forEach((item, index) => {
        item.createdBy = `${user.firstName} ${user.lastName}` || 'System';
        item.availableQuantity = item.actualQuantity;
        item.serialNumber = CoreUtils.generateSerialNumber(
          department.name,
          item.name,
          'EGFM',
          count + index,
        );
      });
    }

    // Validate all items
    await Promise.all(
      items.map(async (item) => {
        await this.itemValidator.validate(item, DatabaseAction.CREATE);
        await this.validateDependencies(item);
      }),
    );

    await this.itemRepository.save(items);
  }

  async update(item: Item): Promise<Item | undefined> {
    await this.itemValidator.validate(item, DatabaseAction.UPDATE);
    await this.validateDependencies(item);
    return this.itemRepository.save(item);
  }

  async updateItem(id: number, updateItemDto: ItemDto, user: User) {
    const item = await this.findByPk(id);
    if (!item) {
      throw new NotFoundException('Item not found');
    }
    await this.classMapper.mutateAsync(updateItemDto, item, ItemDto, Item);
    item.updatedBy = `${user.firstName} ${user.lastName}`;
    await this.update(item);
  }

  async getItem(id: number) {
    const item = await this.findByPk(id);
    if (!item) {
      throw new NotFoundException('Item not found');
    }

    // Fetch store and department concurrently
    const [existingStore, existingDepartment] = await Promise.all([
      this.storeService.findByPk(item.storeId),
      this.departmentService.findByPk(item.departmentId),
    ]);

    if (!existingStore || !existingDepartment) {
      throw new NotFoundException(
        'No store or department is associated with this item',
      );
    }

    const itemDto = await this.classMapper.mapAsync(item, Item, ItemDto);
    itemDto.department = await this.classMapper.mapAsync(
      existingDepartment,
      Department,
      DepartmentDto,
    );
    itemDto.store = await this.classMapper.mapAsync(
      existingStore,
      Store,
      StoreDto,
    );

    return itemDto;
  }

  async getItemsAssociatedWithDepartment(departmentId: number) {
    const department = await this.departmentService.findByPk(departmentId);
    if (!department) {
      throw new NotFoundException('Department not found');
    }

    const items = await this.fetchItemsByDepartment(departmentId);

    // Extract all storeIds from the items in a new array. For each store, fetch the store details and put just id and name in array of objects
    const storeList = await this.storeService.fetchStoresByPks(
      items.map((item) => item.storeId),
    );
    // Map items to DepartmentItemDto
    const departmentItems = await this.classMapper.mapArrayAsync(
      items,
      Item,
      DepartmentItemDto,
    );
    // Compare the departmentItemDtoList with storeList. If the storeId in departmentItemDtoList matches the id in storeList, add the store name to the departmentItemDtoList
    departmentItems.forEach((departmentItem) => {
      const store = storeList.find(
        (store) => store.id === departmentItem.storeId,
      );
      departmentItem.storeName = store.name;
    });

    return departmentItems;
  }

  async getPaginatedItemsAssociatedWithDepartment(
    departmentId: number,
    params: PaginationQueryParams,
    route: string,
  ) {
    const department = await this.departmentService.findByPk(departmentId);
    if (!department) {
      throw new NotFoundException('Department not found');
    }

    const { page, limit, search, filter } = params;
    const where = { departmentId };

    if (search) {
      where['name'] = ILike(`%${search}%`);
    }

    if (filter) {
      where['status'] = filter;
    }

    const options = {
      page,
      limit: limit > 100 ? 100 : limit, // limit the pagination to 100
      route,
    };

    const pagination = await this.paginate(options, where);

    // Get related store entities
    const storeList = await this.storeService.fetchStoresByPks(
      pagination.items.map((item) => item.storeId),
    );

    return {
      items: pagination.items,
      meta: pagination.meta,
      links: pagination.links,
      storeList,
    };
  }
  async findAll(): Promise<Array<Item>> {
    return this.itemRepository.find();
  }

  async findByPk(id: number): Promise<Item> {
    return this.itemRepository.findOneBy({ id });
  }

  async findFirstBy(where: any): Promise<Item> {
    return this.itemRepository.findOneBy(where);
  }

  async delete(id: number): Promise<void> {
    await this.itemRepository.delete(id);
  }

  async activate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const item: Item = await this.findByPk(id);
      item.status = EntityStatus.ACTIVE;
      await this.update(item);
    });
  }

  async deactivate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const item: Item = await this.findByPk(id);
      item.status = EntityStatus.INACTIVE;
      await this.update(item);
    });
  }

  async getPaginatedItems(params: PaginationQueryParams, route: string) {
    const { page, limit, search, filter } = params;
    const where = {};
    if (search) {
      where['name'] = ILike(`%${search}%`);
    }
    if (filter) {
      where['createdDate'] = filter;
      where['status'] = filter;
    }

    const options = {
      page,
      limit: limit > 100 ? 100 : limit, // limit the pagination to 100
      route,
    };

    const pagination = await this.paginate(options, where);

    const storeIds = pagination.items.map((item) => item.storeId);
    const departmentIds = pagination.items.map((item) => item.departmentId);

    const [stores, departments] = await Promise.all([
      this.storeService.fetchStoresByPks(storeIds),
      this.departmentService.fetchDepartmentsByPks(departmentIds),
    ]);

    const storeMap = _.keyBy(stores, 'id');
    const departmentMap = _.keyBy(departments, 'id');

    const dtoList = await Promise.all(
      pagination.items.map(async (item) => {
        const itemDto = await this.classMapper.mapAsync(item, Item, ItemDto);
        itemDto.store = storeMap[item.storeId];
        itemDto.department = departmentMap[item.departmentId];
        return itemDto;
      }),
    );

    return new Pagination(dtoList, pagination.meta, pagination.links);
  }

  async paginate(
    options: IPaginationOptions,
    where?: any,
  ): Promise<Pagination<Item>> {
    if (where && Object.keys(where).length > 0) {
      return paginate<Item>(this.itemRepository, options, {
        where,
        order: {
          createdAt: 'DESC',
        },
      });
    }
    return paginate<Item>(this.itemRepository, options);
  }

  // server side search without pagination
  async search(query: string): Promise<Array<Item>> {
    return await this.itemRepository.find({
      where: [{ name: ILike(`%${query}%`) }],
    });
  }

  // server side search with pagination
  async paginatedSearch(
    query: string,
    page = 1,
    limit = 10,
  ): Promise<Array<Item>> {
    const [results] = await this.itemRepository.findAndCount({
      where: [{ name: ILike(`%${query}%`) }],
      skip: (page - 1) * limit,
      take: limit,
    });
    return results;
  }

  async fetchItemsByDepartment(departmentId: number): Promise<Array<Item>> {
    return Promise.resolve(
      await this.itemRepository.find({
        where: { departmentId: departmentId },
      }),
    );
  }

  async fetchItemsByIds(ids: number[]): Promise<Array<Item>> {
    // Use a set to avoid duplicate IDs and improve efficiency
    const uniqueIds = Array.from(new Set(ids));

    // Ensure all items are unique and exist
    return await Promise.all(
      uniqueIds.map(async (id) => {
        const item = await this.findByPk(id);
        if (!item) {
          throw new NotFoundException(`Item with id ${id} does not exist`);
        }
        return item;
      }),
    );
  }

  async deleteItems(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        await this.delete(id);
      }),
    );
  }

  async deleteAnItem(id: number): Promise<void> {
    const item = await this.findByPk(id);
    if (!item) {
      throw new NotFoundException('Item not found');
    }
    await this.delete(id);
  }

  async modifyItemCondition(itemId: number, condition: Condition, user: User) {
    const item = await this.findByPk(itemId);
    if (!item) {
      throw new NotFoundException('Item not found');
    }

    item.condition = condition;
    item.updatedBy = `${user.firstName} ${user.lastName}`;
    await this.update(item);

    return item;
  }
}
