import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateItemAudit1747609588692 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('item_audit', [
      new TableColumn({
        name: 'returned_date',
        type: 'timestamp',
        isNullable: true,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('item_audit', 'returned_date');
  }
}
