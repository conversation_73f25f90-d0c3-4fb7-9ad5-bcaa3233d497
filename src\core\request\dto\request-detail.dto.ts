import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail } from 'class-validator';
import { RequestSummaryDto } from '@core/request/request-summary/dto/request-summary.dto';
import { ItemAuditDto } from '@core/store/item-audit/dto/item-audit.dto';

export class RequestDetailDto {
  @AutoMap()
  @ApiProperty()
  ministryName: string;

  @AutoMap()
  @ApiProperty()
  churchName: string;

  @AutoMap()
  @ApiProperty()
  requesterName: string;

  @AutoMap()
  @ApiProperty()
  @IsEmail()
  requesterEmail: string;

  @AutoMap()
  @ApiProperty()
  requesterPhone: string;

  @AutoMap()
  @ApiProperty()
  locationOfUse: string;

  @AutoMap()
  @ApiProperty()
  durationOfUse: Date;

  @AutoMap()
  @ApiProperty()
  dateOfReturn: Date;

  @AutoMap()
  @ApiProperty()
  descriptionOfRequest: string;

  @AutoMap(() => RequestSummaryDto)
  @ApiProperty({ type: RequestSummaryDto })
  summary: RequestSummaryDto;

  @AutoMap(() => ItemAuditDto)
  @ApiProperty({ type: ItemAuditDto, isArray: true })
  items: Array<ItemAuditDto>;
}
