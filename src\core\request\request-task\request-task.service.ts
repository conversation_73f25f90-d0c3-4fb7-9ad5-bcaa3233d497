import { Injectable } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Request } from '../entities/request.entity';
import { RequestStatus } from '@common/enums/request-status.enum';
import * as _ from 'lodash';
import { MailService } from '../../notification/mail-service/mail.service';
import { ConfigService } from '@nestjs/config';
import { DateTime } from 'luxon';
import { UserService } from '../../security/user/user.service';
import { ItemService } from '@core/store/item/item.service';

@Injectable()
export class RequestTaskService {
  private readonly baseUrl: string;
  private readonly approvalPath: string;
  private readonly returnFormPath: string;

  constructor(
    @InjectRepository(Request)
    private readonly requestRepository: Repository<Request>,
    private readonly mailService: MailService,
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly itemService: ItemService,
  ) {
    this.baseUrl = this.configService.get<string>('baseUrl');
    this.approvalPath = this.configService.get<string>('approvalPath');
    this.returnFormPath = this.configService.get<string>('returnFormPath');
  }

  @Cron(CronExpression.EVERY_HOUR)
  async sendReturnReminderMail() {
    const assignedRequests = await this.requestRepository.find({
      where: { summary: { requestStatus: RequestStatus.ASSIGNED } },
    });

    if (_.isEmpty(assignedRequests)) return;

    const now = DateTime.now();

    await Promise.all(
      assignedRequests.map(async (request) => {
        const returnDue =
          now >= DateTime.fromISO(request.dateOfReturn.toISOString());
        if (!returnDue) return;

        const link = `${this.baseUrl}/${this.returnFormPath}/${request.id}`;
        const entityName = request.isChurch
          ? request.churchName
          : request.ministryName;

        const itemsList = await Promise.all(
          request.items.map(async (item) => {
            const actualItem = await this.itemService.findByPk(item.itemId);
            return `* ${actualItem.name} - ${item.quantityLeased}`;
          }),
        );

        const itemsDescription = itemsList.join('\n');

        await this.mailService.sendSmtpMail(
          request.requesterEmail,
          'Return Reminder',
          `Hello ${request.requesterName},
          \n This is to remind you that the date of return for the items you requested from ${entityName} is due.
          \n These are the items you requested for: \n${itemsDescription}
          \n Kindly return the items.
          \n God bless you as you do so.`,
        );

        await this.mailService.sendSmtpMail(
          request.requesterHodEmail,
          'Return Reminder',
          `Hello ${request.requesterHodName},
          \n This is to remind you that the date of return for the items requested by ${request.requesterName} from ${entityName} is due.
          \n Kindly remind ${request.requesterName} to return the items.
          \n God bless you as you do so.`,
        );

        const assignedPersonnel = await this.userService.findByPk(
          request.summary.audit.assignee,
        );
        if (!assignedPersonnel) return;

        await this.mailService.sendSmtpMail(
          assignedPersonnel.email,
          'Return Reminder',
          `Hello ${assignedPersonnel.firstName},
          \n This is to remind you that the date of return for the items requested by ${request.requesterName} from ${entityName} is due.
          \n Kindly confirm if the items have been returned via this link: ${link}.
          \n God bless you as you do so.`,
        );
      }),
    );
  }
}
