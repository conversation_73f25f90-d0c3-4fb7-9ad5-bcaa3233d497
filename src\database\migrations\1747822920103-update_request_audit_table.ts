import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateRequestAuditTable1747822920103
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('request_audit', [
      new TableColumn({
        name: 'completed_by',
        type: 'varchar',
        isNullable: true,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('request_audit', 'completed_by');
  }
}
