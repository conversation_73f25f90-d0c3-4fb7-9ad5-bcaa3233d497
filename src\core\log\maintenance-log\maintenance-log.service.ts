import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityStatus } from '@common/entities/base.entity';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { PaginationQueryParams } from '@common/types';
import { User } from '@core/security/user/entities/user.entity';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  IPaginationOptions,
  paginate,
  Pagination,
} from 'nestjs-typeorm-paginate';
import { ILike, Repository } from 'typeorm';
import { CreateMaintenanceLogDto } from './dto/create-maintenance-log.dto';
import { MaintenanceLogDto } from './dto/maintenance-log.dto';
import { UpdateMaintenanceLogDto } from './dto/update-maintenance-log.dto';
import { MaintenanceLog } from './entities/maintenance-log.entity';
import { MaintenanceLogValidator } from './maintenance-log.validator';

@Injectable()
export class MaintenanceLogService {
  constructor(
    @InjectRepository(MaintenanceLog)
    private readonly maintenanceLogRepository: Repository<MaintenanceLog>,
    private readonly maintenanceLogValidator: MaintenanceLogValidator,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  async create(createMaintenanceLogDto: CreateMaintenanceLogDto, user: User) {
    const maintenanceLog: MaintenanceLog = await this.classMapper.mapAsync(
      createMaintenanceLogDto,
      CreateMaintenanceLogDto,
      MaintenanceLog,
    );
    maintenanceLog.createdBy = `${user.firstName} ${user.lastName}` || 'System';
    await this.maintenanceLogValidator.validate(
      maintenanceLog,
      DatabaseAction.CREATE,
    );
    await this.maintenanceLogRepository.save(maintenanceLog);
  }

  async updateMaintenanceLog(
    id: number,
    updateMaintenanceDto: UpdateMaintenanceLogDto,
    user: User,
  ) {
    const maintenanceLog = await this.findByPk(id);
    if (!maintenanceLog) {
      throw new NotFoundException('Maintenance log not found');
    }
    await this.classMapper.mutateAsync(
      updateMaintenanceDto,
      maintenanceLog,
      MaintenanceLogDto,
      MaintenanceLog,
    );
    maintenanceLog.updatedBy = `${user.firstName} ${user.lastName}` || 'System';
    await this.update(maintenanceLog);
  }
  async getMaintenanceLog(id: number) {
    const maintenanceLog = await this.findByPk(id);
    if (!maintenanceLog) {
      throw new NotFoundException('Maintenance log not found');
    }
    return await this.classMapper.mapAsync(
      maintenanceLog,
      MaintenanceLog,
      MaintenanceLog,
    );
  }

  async getPaginatedMaintenanceLogs(
    params: PaginationQueryParams,
    route: string,
  ) {
    const { page, limit, search, filter } = params;
    const where = {};
    if (search) {
      where['artisanName'] = ILike(`%${search}%`);
    }

    const options = {
      page,
      limit: limit > 100 ? 100 : limit, // limit the pagination to 100
      route,
    };

    const pagination = await this.paginate(options, where);

    const dtoList = await this.classMapper.mapArrayAsync(
      pagination.items,
      MaintenanceLog,
      MaintenanceLogDto,
    );
    return new Pagination(dtoList, pagination.meta, pagination.links);
  }

  async update(maintenanceLog: MaintenanceLog) {
    await this.maintenanceLogValidator.validate(
      maintenanceLog,
      DatabaseAction.UPDATE,
    );
    await this.maintenanceLogRepository.save(maintenanceLog);
  }

  async findAll(): Promise<Array<MaintenanceLog>> {
    return this.maintenanceLogRepository.find();
  }

  async findByPk(id: number): Promise<MaintenanceLog> {
    return this.maintenanceLogRepository.findOneBy({ id });
  }

  async findFirstBy(where: any): Promise<MaintenanceLog> {
    return this.maintenanceLogRepository.findOneBy(where);
  }

  async delete(id: number): Promise<void> {
    await this.maintenanceLogRepository.delete(id);
  }

  async activate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const maintenanceLog: MaintenanceLog = await this.findByPk(id);
      maintenanceLog.status = EntityStatus.ACTIVE;
      await this.update(maintenanceLog);
    });
  }

  async deactivate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const maintenanceLog: MaintenanceLog = await this.findByPk(id);
      maintenanceLog.status = EntityStatus.INACTIVE;
      await this.update(maintenanceLog);
    });
  }

  async paginate(
    options: IPaginationOptions,
    where?: any,
  ): Promise<Pagination<MaintenanceLog>> {
    if (where && Object.keys(where).length > 0) {
      return paginate<MaintenanceLog>(this.maintenanceLogRepository, options, {
        where,
        order: {
          createdAt: 'DESC',
        },
      });
    }
    return paginate<MaintenanceLog>(this.maintenanceLogRepository, options);
  }

  // server side search without pagination
  async search(query: string): Promise<Array<MaintenanceLog>> {
    return await this.maintenanceLogRepository.find({
      where: [{ artisanName: ILike(`%${query}%`) }],
    });
  }

  // server side search with pagination
  async paginatedSearch(
    query: string,
    page = 1,
    limit = 10,
  ): Promise<Array<MaintenanceLog>> {
    const [results] = await this.maintenanceLogRepository.findAndCount({
      where: [{ artisanName: ILike(`%${query}%`) }],
      skip: (page - 1) * limit,
      take: limit,
    });
    return results;
  }
}
