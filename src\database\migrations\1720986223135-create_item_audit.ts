import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateItemAudit1720986223135 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'item_audit',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'store_name',
            type: 'varchar',
          },
          {
            name: 'item_id',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'quantity_leased_out',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'leased_out_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'condition_before_leased_out',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'quantity_returned',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'released_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'condition_after_leased_out',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'audit_completed',
            type: 'boolean',
            default: false,
          },
          {
            name: 'audit_completed_date',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Add an index to the table.
    await queryRunner.createIndex(
      'item_audit',
      new TableIndex({
        name: 'IDX_NAME',
        columnNames: ['store_name'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('item_audit', 'IDX_STORE_NAME');
    await queryRunner.dropTable('item_audit');
  }
}
