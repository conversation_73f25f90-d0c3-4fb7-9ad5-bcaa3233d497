import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateUser1721660495503 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('user', 'member_type');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'user',
      new TableColumn({
        name: 'member_type',
        type: 'varchar',
      }),
    );
  }
}
