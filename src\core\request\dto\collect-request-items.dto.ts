import { AutoMap } from '@automapper/classes';
import { CreateItemAuditDto } from '@core/store/item-audit/dto/create-item-audit.dto';
import { ApiProperty } from '@nestjs/swagger';
// import { CreateItemAuditDto } from '@core/store/item-audit/dto/return-item-audit.dto';

export class CollectRequestItemsDto {
  @AutoMap(() => CreateItemAuditDto)
  @ApiProperty({ type: CreateItemAuditDto, isArray: true })
  items: Array<CreateItemAuditDto>;
}
