import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { RequestAuditDto } from '../../request-audit/dto/request-audit.dto';
import { RequestStatus } from '@common/enums/request-status.enum';
import { EntityDto } from '@common/dto/base.dto';

export class RequestSummaryDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  assignerId: number;

  @AutoMap()
  @ApiProperty()
  assigneeId: number;

  @AutoMap()
  @ApiProperty()
  assigneeName: string;

  @AutoMap()
  @ApiProperty()
  dateAssigned: Date;

  @AutoMap()
  @ApiProperty({
    type: 'string',
    enum: Object.values(RequestStatus),
    name: 'request_status',
    description: 'Possible request statuses',
  })
  requestStatus: RequestStatus;

  @AutoMap()
  @ApiProperty()
  audit: RequestAuditDto;
}
