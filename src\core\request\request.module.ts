import { Module } from '@nestjs/common';
import { RequestService } from './request.service';
import { RequestController } from './request.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AutomapperModule } from '@automapper/nestjs';
import { RequestMapper } from './request.mapper.service';
import { Request } from './entities/request.entity';
import { RequestValidator } from './request.validator';
import { RequestTaskService } from './request-task/request-task.service';
import { NotificationModule } from '../notification/notification.module';
import { LoggerModule } from '@common/logger/logger.module';
import { UserModule } from '../security/user/user.module';
import { StoreModule } from '../store/store.module';
import { ItemModule } from '@core/store/item/item.module';
import { ItemAuditModule } from '@core/store/item-audit/item-audit.module';
import { DepartmentModule } from '@core/security/department/department.module';
import { RequestSummaryModule } from '@core/request/request-summary/request-summary.module';
import { RequestAuditModule } from '@core/request/request-audit/request-audit.module';

@Module({
  controllers: [RequestController],
  exports: [RequestService],
  imports: [
    TypeOrmModule.forFeature([Request]),
    AutomapperModule,
    NotificationModule,
    LoggerModule,
    ItemModule,
    ItemAuditModule,
    UserModule,
    StoreModule,
    DepartmentModule,
    RequestSummaryModule,
    RequestAuditModule,
  ],
  providers: [
    RequestService,
    RequestMapper,
    RequestValidator,
    RequestTaskService,
  ],
})
export class RequestModule {}
