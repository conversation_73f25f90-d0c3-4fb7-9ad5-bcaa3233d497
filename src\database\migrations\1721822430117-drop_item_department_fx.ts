import {
  MigrationInterface,
  QueryRunner,
  <PERSON>C<PERSON>umn,
  TableForeignKey,
} from 'typeorm';

export class DropItemDepartmentFx1721822430117 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('item');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('department_id') !== -1,
    );
    await queryRunner.dropForeignKey('item', foreignKey);
    await queryRunner.dropColumn('item', 'department_id');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'item',
      new TableColumn({
        name: 'department_id',
        type: 'bigint',
      }),
    );

    await queryRunner.createForeignKey(
      'item',
      new TableForeignKey({
        columnNames: ['department_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'department',
      }),
    );
  }
}
