import { Module } from '@nestjs/common';
import { StoreService } from './store.service';
import { StoreController } from './store.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Store } from './entities/store.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { StoreProfile } from './store.profile';
import { StoreValidator } from './store.validator';
import { LoggerModule } from '@common/logger/logger.module';
import { ItemUnitModule } from './item-unit/item-unit.module';

@Module({
  controllers: [StoreController],
  exports: [StoreService, StoreValidator],
  imports: [TypeOrmModule.forFeature([Store]), AutomapperModule, LoggerModule, ItemUnitModule],
  providers: [StoreService, StoreProfile, StoreValidator],
})
export class StoreModule {}
