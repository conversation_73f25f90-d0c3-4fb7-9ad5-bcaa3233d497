import { Modu<PERSON> } from '@nestjs/common';
import { StoreService } from './store.service';
import { StoreController } from './store.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Store } from './entities/store.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { StoreProfile } from './store.profile';
import { StoreValidator } from './store.validator';
import { LoggerModule } from '@common/logger/logger.module';

@Module({
  controllers: [StoreController],
  exports: [StoreService, StoreValidator],
  imports: [TypeOrmModule.forFeature([Store]), AutomapperModule, LoggerModule],
  providers: [StoreService, StoreProfile, StoreValidator],
})
export class StoreModule {}
