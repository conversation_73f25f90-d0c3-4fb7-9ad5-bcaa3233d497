import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateItem1720984334095 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    //Create Item table
    await queryRunner.createTable(
      new Table({
        name: 'item',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'name',
            type: 'varchar',
          },
          {
            name: 'quantity',
            type: 'bigint',
          },
          {
            name: 'serial_number',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'fragile',
            type: 'boolean',
          },
          {
            name: 'picture_url',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'condition',
            type: 'varchar',
          },
          {
            name: 'department',
            type: 'varchar',
          },
        ],
      }),
      true,
    );

    // Add an index to the item table.
    await queryRunner.createIndex(
      'item',
      new TableIndex({
        name: 'IDX_ITEM_NAME',
        columnNames: ['name'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    //Drop table and its index
    await queryRunner.dropIndex('item', 'IDX_ITEM_NAME');
    await queryRunner.dropTable('item');
  }
}
