import { RequestStatus } from 'src/common/enums/request-status.enum';
import { ApiProperty } from '@nestjs/swagger';
import { AutoMap } from '@automapper/classes';

export class CreateRequestSummaryDto {
  @AutoMap()
  @ApiProperty()
  assignerId: number;

  @AutoMap()
  @ApiProperty()
  assigneeId: number;

  @AutoMap()
  @ApiProperty()
  dateAssigned: Date;

  @AutoMap()
  @ApiProperty({
    type: 'string',
    enum: Object.values(RequestStatus),
    name: 'request_status',
    description: 'Possible request statuses',
  })
  requestStatus: RequestStatus;
}
