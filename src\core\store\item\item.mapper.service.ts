import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingProfile,
} from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { DepartmentService } from '@core/security/department/department.service';
import { StoreService } from '@core/store/store.service';
import { Injectable } from '@nestjs/common';
import { CreateItemDto } from './dto/create-item.dto';
import { DepartmentItemDto } from './dto/department-item.dto';
import { ItemConditionStatusUpdateDto } from './dto/item-condition-status-update.dto';
import { ItemDto } from './dto/item.dto';
import { Item } from './entities/item.entity';

@Injectable()
export class ItemMapperService extends AutomapperProfile {
  constructor(
    @InjectMapper() mapper: Mapper,
    private readonly departmentService: DepartmentService,
    private readonly storeService: StoreService,
  ) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(
        mapper,
        Item,
        ItemDto,
        forMember(
          (destination) => destination.actualQuantity,
          mapFrom((source) => Number(source.actualQuantity)),
        ),
        forMember(
          (destination) => destination.availableQuantity,
          mapFrom((source) => Number(source.availableQuantity)),
        ),
      );
      createMap(mapper, ItemDto, Item);
      createMap(mapper, CreateItemDto, Item);
      createMap(
        mapper,
        Item,
        DepartmentItemDto,
        forMember(
          (destination) => destination.availableQuantity,
          mapFrom((source) => Number(source.availableQuantity)),
        ),
      );
      createMap(mapper, ItemConditionStatusUpdateDto, Item);
    };
  }
}
