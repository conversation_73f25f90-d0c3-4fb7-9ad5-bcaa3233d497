import { AutoMap } from '@automapper/classes';
import { Column, Entity, OneToMany, OneToOne } from 'typeorm';
import { IsEmail } from 'class-validator';
import { AbstractEntity } from '@common/entities/base.entity';
import { RequestSummary } from '@core/request/request-summary/entities/request-summary.entity';
import { ItemAudit } from '@core/store/item-audit/entities/item-audit.entity';

@Entity({ name: 'request' })
export class Request extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'requester_name', type: 'varchar' })
  requesterName: string;

  @AutoMap()
  @IsEmail()
  @Column({ name: 'requester_email', type: 'varchar' })
  requesterEmail: string;

  @AutoMap()
  @Column({ name: 'requester_phone', type: 'varchar' })
  requesterPhone: string;

  @AutoMap()
  @Column({ name: 'is_ministry', type: 'boolean' })
  isMinistry: boolean;

  @AutoMap()
  @Column({ name: 'ministry_name', type: 'varchar' })
  ministryName: string;

  @AutoMap()
  @Column({ name: 'is_church', type: 'boolean', default: false })
  isChurch: boolean;

  @AutoMap()
  @Column({ name: 'church_name', type: 'varchar', default: false })
  churchName: string;

  @AutoMap()
  @Column({ name: 'requester_department_id', type: 'bigint' })
  requesterDepartmentId: number;

  @AutoMap()
  @Column({ name: 'requester_hod_name', type: 'varchar' })
  requesterHodName: string;

  @AutoMap()
  @IsEmail()
  @Column({ name: 'requester_hod_email', type: 'varchar' })
  requesterHodEmail: string;

  @AutoMap()
  @Column({ name: 'requester_hod_phone', type: 'varchar' })
  requesterHodPhone: string;

  @AutoMap()
  @Column({ name: 'location_of_use', type: 'varchar' })
  locationOfUse: string;

  @AutoMap()
  @Column({ name: 'duration_of_use', type: 'timestamp' })
  durationOfUse: Date;

  @AutoMap()
  @Column({ name: 'date_of_return', type: 'timestamp' })
  dateOfReturn: Date;

  @AutoMap()
  @Column({ name: 'description_of_request', type: 'text' })
  descriptionOfRequest: string;

  @AutoMap()
  @OneToOne(() => RequestSummary, (summary) => summary.request, {
    eager: true,
    cascade: true,
    onDelete: 'CASCADE',
    orphanedRowAction: 'delete',
    onUpdate: 'CASCADE',
  })
  summary: RequestSummary;

  @AutoMap()
  @OneToMany(() => ItemAudit, (itemAudit) => itemAudit.request, {
    cascade: true,
    eager: true,
    orphanedRowAction: 'delete',
  })
  items: Array<ItemAudit>;
}
