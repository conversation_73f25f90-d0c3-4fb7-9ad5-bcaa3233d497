import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateUser1721816708138 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'user',
      new TableColumn({
        name: 'token',
        type: 'text',
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      'user',
      new TableColumn({
        name: 'expires_at',
        type: 'int',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('user', 'token');
    await queryRunner.dropColumn('user', 'expires_at');
  }
}
