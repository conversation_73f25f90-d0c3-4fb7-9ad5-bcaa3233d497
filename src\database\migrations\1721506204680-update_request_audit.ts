import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateRequestAudit1721506204680 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.changeColumn('request_audit', 'assignee',
          new TableColumn({
            name: 'assignee',
            type: 'bigint',
            isNullable: true,
        }),)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.changeColumn('request_audit', 'assignee',       new TableColumn({
            name: 'assignee',
            type: 'varchar',
            isNullable: true,
        }),)
    }

}
