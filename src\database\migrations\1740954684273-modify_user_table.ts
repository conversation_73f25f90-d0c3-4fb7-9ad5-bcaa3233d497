import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class ModifyUserTable1740954684273 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'user',
      new TableColumn({
        name: 'refresh_token',
        type: 'text',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('user', 'refresh_token');
  }
}
