import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { createMap, Mapper, MappingProfile } from '@automapper/core';
import { Store } from './entities/store.entity';
import { StoreDto } from './dto/store.dto';
import { CreateStoreDto } from './dto/create-store.dto';

@Injectable()
export class StoreProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, Store, StoreDto);
      createMap(mapper, StoreDto, Store);
      createMap(mapper, CreateStoreDto, Store);
    };
  }
}
