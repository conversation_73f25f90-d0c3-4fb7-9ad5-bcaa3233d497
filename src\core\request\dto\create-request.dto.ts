import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail } from 'class-validator';
import { CreateItemAuditDto } from '@core/store/item-audit/dto/create-item-audit.dto';

export class CreateRequestDto {
  @AutoMap()
  @ApiProperty()
  requesterName: string;

  @AutoMap()
  @IsEmail()
  @ApiProperty()
  requesterEmail: string;

  @AutoMap()
  @ApiProperty()
  requesterPhone: string;

  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
  })
  isMinistry: boolean;

  @AutoMap()
  @ApiProperty()
  ministryName: string;

  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
  })
  isChurch: boolean;

  @AutoMap()
  @ApiProperty()
  churchName: string;

  @AutoMap()
  @ApiProperty()
  requesterDepartmentId: number;

  @AutoMap()
  @ApiProperty()
  locationOfUse: string;

  // @AutoMap()
  // @ApiProperty()
  // durationOfUse: Date;

  @AutoMap()
  @ApiProperty()
  dateOfReturn: Date;

  @AutoMap()
  @ApiProperty()
  descriptionOfRequest: string;

  @AutoMap(() => CreateItemAuditDto)
  @ApiProperty({ type: CreateItemAuditDto, isArray: true })
  items: Array<CreateItemAuditDto>;
}
