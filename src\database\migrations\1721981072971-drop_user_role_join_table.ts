import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class DropUserRoleJoinTable1721981072971 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('user_role');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'user_role',
        columns: [
          {
            name: 'user_id',
            type: 'bigint',
            isPrimary: true,
          },
          {
            name: 'role_id',
            type: 'bigint',
            isPrimary: true,
          },
        ],
      }),
    );
  }
}
