import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { ReturnItemAuditDto } from '@core/store/item-audit/dto/return-item-audit.dto';
import { CreateItemAuditDto } from '@core/store/item-audit/dto/create-item-audit.dto';

export class ReturnRequestDto {
  @AutoMap(() => CreateItemAuditDto)
  @ApiProperty({ type: CreateItemAuditDto, isArray: true })
  items: Array<CreateItemAuditDto>;
  // @AutoMap(() => ReturnItemAuditDto)
  // @ApiProperty({ type: ReturnItemAuditDto, isArray: true })
  // items: Array<ReturnItemAuditDto>;
}
