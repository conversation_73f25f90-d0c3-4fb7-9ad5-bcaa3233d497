import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  UploadApiErrorResponse,
  UploadApiResponse,
  v2 as cloudinary,
} from 'cloudinary';
import { CloudinaryStorageOption } from '../../../config/storage';
import { LoggerService } from '../../../common/logger/logger.service';
import { StorageInterface } from '../storage.interface';

@Injectable()
export class CloudinaryService implements StorageInterface {
  private readonly storageConfig: CloudinaryStorageOption;

  constructor(
    private readonly logger: LoggerService,
    private configService: ConfigService,
  ) {
    this.logger.setContext(CloudinaryService.name);
    this.storageConfig =
      this.configService.get<CloudinaryStorageOption>('storage.cloudinary');
    if (this.storageConfig) {
      cloudinary.config({
        cloud_name: this.storageConfig.cloudName,
        api_key: this.storageConfig.key,
        api_secret: this.storageConfig.secret,
      });
      this.logger.log('Cloudinary configuration initialized.');
    } else {
      this.logger.error('Cloudinary configuration not found.');
      throw new NotFoundException('Cloudinary configuration not found.');
    }
  }

  async deleteFile(publicId: string, signature?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      cloudinary.uploader.destroy(publicId, (error, result) =>
        error ? reject(error) : resolve(result),
      );
    });
  }

  async uploadFile(
    file: Buffer,
    folder: string,
    extension?: string,
  ): Promise<UploadApiResponse | UploadApiErrorResponse> {
    return new Promise((resolve, reject) => {
      cloudinary.uploader.upload(
        `data:image/${extension};base64,${file.toString('base64')}`,
        {
          folder: folder,
          unique_filename: true,
          format: extension,
        },
        (error, result) => (error ? reject(error) : resolve(result)),
      );
    });
  }
}
