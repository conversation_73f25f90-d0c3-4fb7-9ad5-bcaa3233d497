import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateRequestAudit1720949991524 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create request_audit table
    await queryRunner.createTable(
      new Table({
        name: 'request_audit',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'assigned',
            type: 'boolean',
            default: false,
          },
          {
            name: 'assigner',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'assignee',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'date_assigned',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'completed',
            type: 'boolean',
            default: false,
          },
          {
            name: 'completed_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'completed_by',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'from_what_store',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    /**
     * Create Index
     */
    // Add an index to the user table.
    await queryRunner.createIndex(
      'request_audit',
      new TableIndex({
        name: 'IDX_REQUEST_ASSIGNER',
        columnNames: ['assigner'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    //Drop user table and its index
    await queryRunner.dropIndex('request_audit', 'IDX_REQUEST_ASSIGNER');
    await queryRunner.dropTable('request_audit');
  }
}
