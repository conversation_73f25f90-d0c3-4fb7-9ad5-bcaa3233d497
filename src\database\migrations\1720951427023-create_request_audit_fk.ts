import {
  MigrationInterface,
  QueryRunner,
  <PERSON><PERSON><PERSON>umn,
  TableForeign<PERSON>ey,
} from 'typeorm';

export class CreateRequestAuditFk1720951427023 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'request_audit',
      new TableColumn({
        isNullable: true,
        name: 'request_summary_id',
        type: 'bigint',
      }),
    );

    // Create FX for request_audit table
    await queryRunner.createForeignKey(
      'request_audit',
      new TableForeignKey({
        columnNames: ['request_summary_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'request_summary',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the foreign key first
    const table = await queryRunner.getTable('request_audit');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('request_summary_id') !== -1,
    );
    await queryRunner.dropForeignKey('request_audit', foreignKey);
  }
}
