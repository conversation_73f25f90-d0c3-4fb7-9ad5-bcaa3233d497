import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityStatus } from '@common/entities/base.entity';
import { ComplaintStatus } from '@common/enums/complaint-status.enum';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { LoggerService } from '@common/logger/logger.service';
import { PaginationQueryParams } from '@common/types';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { paginate, Pagination } from 'nestjs-typeorm-paginate';
import { ILike, Repository } from 'typeorm';
import { ComplaintValidator } from './complaint.validator';
import { ComplaintTableDto } from './dto/complaint-table.dto';
import { ComplaintDto } from './dto/complaint.dto';
import { CreateComplaintDto } from './dto/create-complaint.dto';
import { Complaint } from './entities/complaint.entity';
import { ComplaintSummary } from '@core/complaint/complaint-summary/entities/complaint-summary.entity';

@Injectable()
export class ComplaintService {
  constructor(
    @InjectRepository(Complaint)
    private readonly complaintRepository: Repository<Complaint>,
    private readonly logger: LoggerService,
    private readonly complaintValidator: ComplaintValidator,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(ComplaintService.name);
  }

  async findByPk(id: number): Promise<Complaint> {
    return await this.complaintRepository.findOneBy({ id });
  }

  async delete(id: number): Promise<void> {
    await this.complaintRepository.delete(id);
  }

  async findAll(): Promise<Array<Complaint>> {
    return await this.complaintRepository.find();
  }

  async create(createComplaintDto: CreateComplaintDto) {
    const newComplaint = await this.classMapper.mapAsync(
      createComplaintDto,
      CreateComplaintDto,
      Complaint,
    );
    await this.complaintValidator.validate(newComplaint, DatabaseAction.CREATE);
    newComplaint.complaintDate = new Date();
    const summary = new ComplaintSummary();
    summary.complaintStatus = ComplaintStatus.PENDING;
    summary.attendedTo = false;
    summary.dateResolved = null;
    summary.resolvedBy = null;
    newComplaint.summary = summary;
    await this.complaintRepository.save(newComplaint);
  }

  async activate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const complaint: Complaint = await this.findByPk(id);
      complaint.status = EntityStatus.ACTIVE;
      complaint.summary.status = EntityStatus.ACTIVE;
      await this.update(complaint);
    });
  }

  async deactivate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const complaint: Complaint = await this.findByPk(id);
      complaint.status = EntityStatus.INACTIVE;
      complaint.summary.status = EntityStatus.INACTIVE;
      await this.update(complaint);
    });
  }

  async getPaginatedComplaints(
    params: PaginationQueryParams,
    route: string,
  ): Promise<Pagination<ComplaintTableDto>> {
    const { page, limit, search, filter } = params;
    const where = {};
    if (search) {
      where['complainerName'] = ILike(`%${search}%`);
      where['complaintSubject'] = ILike(`%${search}%`);
    }
    if (filter) {
      where['status'] = filter;
      where['summary.complaintStatus'] = filter;
    }

    const options = {
      page,
      limit: limit > 100 ? 100 : limit, // limit the pagination to 100
      route,
    };

    const pagination = await paginate<Complaint>(
      this.complaintRepository,
      options,
      {
        where,
        order: {
          createdAt: 'DESC',
        },
      },
    );

    const mappedData = await this.classMapper.mapArrayAsync(
      pagination.items,
      Complaint,
      ComplaintTableDto,
    );

    return new Pagination(mappedData, pagination.meta, pagination.links);
  }

  async getComplaint(complaintId: number): Promise<ComplaintDto> {
    const complaint = await this.findByPk(complaintId);
    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }
    return await this.classMapper.mapAsync(complaint, Complaint, ComplaintDto);
  }

  async update(complaint: Complaint) {
    await this.complaintValidator.validate(complaint, DatabaseAction.UPDATE);
    await this.complaintRepository.save(complaint);
  }

  async paginatedSearch(
    query: string,
    page: number,
    limit: number,
  ): Promise<Array<Complaint>> {
    const [results] = await this.complaintRepository.findAndCount({
      where: [{ complainerName: ILike(`%${query}%`) }],
      skip: (page - 1) * limit,
      take: limit,
    });
    return Promise.resolve(results);
  }

  async search(query: string): Promise<Array<Complaint>> {
    return Promise.resolve(
      await this.complaintRepository.find({
        where: [
          { complainerName: ILike(`%${query}%`) },
          { complaintSubject: ILike(`%${query}%`) },
        ],
      }),
    );
  }
}
