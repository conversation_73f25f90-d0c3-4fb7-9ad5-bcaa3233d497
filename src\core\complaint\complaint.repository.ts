import { BaseRepository } from '../../common/repository/base.repository';
import { IEntity } from '../../common/interface/IEntity';
import { Complaint } from './entities/complaint.entity';
import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { DataSource } from 'typeorm';
import { Request } from 'express';

@Injectable({ scope: Scope.REQUEST })
export class ComplaintRepository
  extends BaseRepository
  implements IEntity<Complaint>
{
  constructor(dataSource: DataSource, @Inject(REQUEST) request: Request) {
    super(dataSource, request);
  }

  async create(entity: Complaint): Promise<Complaint> {
    return this.getRepository(Complaint).create(entity);
  }

  async delete(id: number): Promise<void> {
    await this.getRepository(Complaint).delete(id);
  }

  async findAll(): Promise<Array<Complaint>> {
    return await this.getRepository(Complaint).find({
      relations: {
        summary: true,
      },
    });
  }

  async findByPk(id: number): Promise<Complaint> {
    return await this.getRepository(Complaint).findOne({
      where: { id },
      relations: {
        summary: true,
      },
    });
  }

  async update(entity: Complaint): Promise<Complaint> {
    return await this.getRepository(Complaint).save(entity);
  }

  async insert(entity: Complaint): Promise<void> {
    await this.getRepository(Complaint).insert(entity);
  }

  async findAndCount(options: object): Promise<[Complaint[], number]> {
    return await this.getRepository(Complaint).findAndCount(options);
  }

  async count(options: object): Promise<number> {
    return this.getRepository(Complaint).count(options);
  }

  async find(options: object): Promise<Array<Complaint>> {
    return this.getRepository(Complaint).find(options);
  }

  getEntityRepository(): any {
    return this.getRepository(Complaint);
  }
}
