import { Column, Entity } from 'typeorm';
import { AbstractEntity } from '@common/entities/base.entity';
import { Condition } from '@common/enums/condition.enum';
import { AutoMap } from '@automapper/classes';

@Entity({ name: 'item' })
export class Item extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'name', type: 'varchar', unique: true })
  name: string;

  @AutoMap()
  @Column({ name: 'available_quantity', default: 0, type: 'bigint' })
  availableQuantity: number;

  @AutoMap()
  @Column({ name: 'actual_quantity', default: 0, type: 'bigint' })
  actualQuantity: number;

  @AutoMap()
  @Column({ name: 'serial_number', type: 'varchar', unique: true })
  serialNumber: string; //TODO: Write a serial number generator function

  @AutoMap()
  @Column({ name: 'store_id', type: 'bigint' })
  storeId: number;

  @AutoMap()
  @Column({ name: 'fragile', type: 'boolean' })
  fragile: boolean;

  @AutoMap()
  @Column({ name: 'picture_url', type: 'varchar' })
  pictureUrl?: string;

  @AutoMap(() => String)
  @Column({
    name: 'condition',
    type: 'enum',
    enum: Condition,
    default: Condition.NOT_SPECIFIED,
  })
  condition: Condition;

  @AutoMap()
  @Column({ name: 'department_id', type: 'bigint' })
  departmentId: number;
}
