import { Modu<PERSON> } from '@nestjs/common';
import { ComplaintService } from './complaint.service';
import { ComplaintController } from './complaint.controller';
import { ComplaintValidator } from './complaint.validator';
import { ComplaintProfile } from './complaint.profile';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Complaint } from './entities/complaint.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { LoggerModule } from '../../common/logger/logger.module';

@Module({
  controllers: [ComplaintController],
  exports: [ComplaintService],
  imports: [
    TypeOrmModule.forFeature([Complaint]),
    AutomapperModule,
    LoggerModule,
  ],
  providers: [ComplaintService, ComplaintValidator, ComplaintProfile],
})
export class ComplaintModule {}
