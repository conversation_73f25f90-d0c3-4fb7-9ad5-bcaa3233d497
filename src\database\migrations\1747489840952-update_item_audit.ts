import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateItemAudit1747489840952 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('item_audit', [
      new TableColumn({
        name: 'quantity_collected',
        type: 'bigint',
        default: 0,
      }),
      new TableColumn({
        name: 'collected_date',
        type: 'timestamp',
        isNullable: true,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('item_audit', 'quantity_collected');
    await queryRunner.dropColumn('item_audit', 'collected_date');
  }
}
