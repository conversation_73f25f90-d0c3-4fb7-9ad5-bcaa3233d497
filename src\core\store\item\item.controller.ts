import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { CurrentUser } from '@common/decorators/current-user.decorator';
import { User } from '@core/security/user/entities/user.entity';
import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import {
  ApiBody,
  ApiExcludeEndpoint,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { Pagination } from 'nestjs-typeorm-paginate';
import { ActivateItemDto } from './dto/activate-item.dto';
import { CreateItemDto } from './dto/create-item.dto';
import { DeactivateItemDto } from './dto/deactivate-item.dto';
import { DepartmentItemDto } from './dto/department-item.dto';
import { ItemDto } from './dto/item.dto';
import { ItemService } from './item.service';
import { Item } from './entities/item.entity';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { CoreUtils } from '@common/utils/core.utils';
import { Public } from '@common/decorators/public.decorator';
import { ItemConditionStatusUpdateDto } from './dto/item-condition-status-update.dto';
import { Store } from '@core/store/entities/store.entity';
import { Department } from '@core/security/department/entities/department.entity';
import { StoreDto } from '@core/store/dto/store.dto';
import { DepartmentDto } from '@core/security/department/dto/department.dto';
import { StoreService } from '@core/store/store.service';
import { DepartmentService } from '@core/security/department/department.service';

@ApiTags('Item')
@Controller({
  path: 'item',
  version: '1',
})
export class ItemController {
  constructor(
    private readonly itemService: ItemService,
    private readonly storeService: StoreService,
    private readonly departmentService: DepartmentService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  @ApiOperation({ summary: 'Create a new item' })
  @ApiBody({ type: CreateItemDto })
  @Post('new')
  async newItem(
    @Body() createItemDto: CreateItemDto,
    @CurrentUser() user: User,
  ) {
    return CoreUtils.handleRequest(async () => {
      const item = await this.classMapper.mapAsync(
        createItemDto,
        CreateItemDto,
        Item,
      );
      const createdItem = await this.itemService.create(item, user);
      const data = await this.classMapper.mapAsync(createdItem, Item, ItemDto);
      return {
        message: 'Item created successfully',
        data,
      };
    });
  }

  @ApiOperation({ summary: 'Create multiple items at once' })
  @ApiBody({ type: CreateItemDto, isArray: true })
  @Post('multiple/new')
  async createMultipleItemsAtOnce(
    @Body() createItemDtos: CreateItemDto[],
    @CurrentUser() user: User,
  ) {
    return CoreUtils.handleRequest(async () => {
      const items = await this.classMapper.mapArrayAsync(
        createItemDtos,
        CreateItemDto,
        Item,
      );
      await this.itemService.createMany(items, user);
      return {
        message: `Item${
          createItemDtos.length > 1 ? 's' : ''
        } created successfully`,
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Update an item' })
  @ApiBody({ type: ItemDto })
  @Patch('update/:itemId')
  async updateItem(
    @Body() updateItemDto: ItemDto,
    @Param('itemId') id: number,
    @CurrentUser() user: User,
  ) {
    return CoreUtils.handleRequest(async () => {
      await this.itemService.updateItem(id, updateItemDto, user);
      return {
        message: 'Item updated successfully',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Retrieve an item' })
  @ApiOkResponse({
    description: 'Item details',
    type: ItemDto,
    isArray: false,
  })
  @Get('detail/:id')
  async retrieveAnItem(@Param('id') id: number) {
    // return CoreUtils.handleRequest(async () => {
    //   const data = await this.itemService.getItem(id);
    //   return {
    //     message: 'Item updated successfully',
    //     data,
    //   };
    // });
  }

  @Public()
  @ApiOperation({ summary: 'Retrieve all items associated with a department' })
  @Get('all/:departmentId')
  @ApiOkResponse({
    description: 'The Department Items',
    type: DepartmentItemDto,
    isArray: true,
  })
  async retrieveAllItemsAssociatedWithADepartment(
    @Param('departmentId') departmentId: number,
  ) {
    // return CoreUtils.handleRequest(async () => {
    //   const data = await this.itemService.getItemsAssociatedWithDepartment(
    //     departmentId,
    //   );
    //   return {
    //     message: 'Item updated successfully',
    //     data,
    //   };
    // });
  }

  @ApiOperation({ summary: 'Make item(s) active.' })
  @ApiBody({ type: ActivateItemDto })
  @Patch('activate')
  async activate(@Body() body: ActivateItemDto) {
    return CoreUtils.handleRequest(async () => {
      await this.itemService.activate(body.ids);
      return {
        message: `Item${body.ids.length > 1 ? 's' : ''} activated successfully`,
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Make item(s) inactive' })
  @ApiBody({ type: DeactivateItemDto })
  @Patch('deactivate')
  async deactivate(@Body() body: DeactivateItemDto) {
    return CoreUtils.handleRequest(async () => {
      await this.itemService.deactivate(body.ids);
      return {
        message: `Item${
          body.ids.length > 1 ? 's' : ''
        } deactivated successfully`,
        data: null,
      };
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get paginated list of items' })
  @ApiQuery({ name: 'page', required: true, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: true, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'filter', required: false, type: String })
  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Successful response with paginated items',
    type: Pagination,
  })
  async getAllItems(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit,
    @Query('search') search: string,
    @Query('filter') filter: string,
    @CurrentRoute() route: string,
  ) {
    // return CoreUtils.handleRequest(async () => {
    //   const { items, meta, links, storeMap, departmentMap } =
    //     await this.itemService.getPaginatedItems(
    //       {
    //         page,
    //         limit,
    //         search,
    //         filter,
    //       },
    //       route,
    //     );
    //   const dtoList = await Promise.all(
    //     items.map(async (item) => {
    //       const itemDto = await this.classMapper.mapAsync(item, Item, ItemDto);
    //       itemDto.store = storeMap[item.storeId];
    //       itemDto.department = departmentMap[item.departmentId];
    //       return itemDto;
    //     }),
    //   );
    //   const data = new Pagination(dtoList, meta, links);
    //   return { message: 'Items retrieved successfully', data };
    // });
  }

  @Get('department/:departmentId')
  @ApiOperation({ summary: 'Get paginated list of department items' })
  @ApiQuery({ name: 'page', required: true, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: true, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'filter', required: false, type: String })
  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Successful response with paginated department items',
    type: Pagination,
  })
  async getPaginatedDepartmentItems(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit,
    @Query('search') search: string,
    @Query('filter') filter: string,
    @Param('departmentId') departmentId: number,
    @CurrentRoute() route: string,
  ) {
    // return CoreUtils.handleRequest(async () => {
    //   const { items, meta, links, storeList } =
    //     await this.itemService.getPaginatedItemsAssociatedWithDepartment(
    //       departmentId,
    //       {
    //         page,
    //         limit,
    //         search,
    //         filter,
    //       },
    //       route,
    //     );
    //   const departmentItemDtos = await this.classMapper.mapArrayAsync(
    //     items,
    //     Item,
    //     DepartmentItemDto,
    //   );
    //   departmentItemDtos.forEach((departmentItemDto) => {
    //     const store = storeList.find(
    //       (store) => store.id === departmentItemDto.storeId,
    //     );
    //     departmentItemDto.storeName = store.name;
    //   });
    //   return {
    //     message: 'Department items retrieved successfully',
    //     data: new Pagination(departmentItemDtos, meta, links),
    //   };
    // });
  }

  // server-side search

  // 1) Without pagination
  @ApiOperation({ summary: 'Search for items' })
  @Get('/search')
  async searchStore(@Query('q') query: string) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.itemService.search(query);
      return {
        message: 'Items retrieved successfully',
        data,
      };
    });
  }

  // 2) With pagination
  @ApiOperation({ summary: 'Paginated search for items' })
  @Get('/paginate-search')
  async paginateSearchStore(
    @Query('q') query: string,
    @Query('page') page: number,
    @Query('limit') limit: number,
  ) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.itemService.paginatedSearch(query, page, limit);
      return {
        message: 'Items retrieved successfully',
        data,
      };
    });
  }

  @ApiExcludeEndpoint()
  @ApiOkResponse({
    description: 'Item deleted successfully',
    isArray: false,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Delete an item' })
  @Delete('/delete/:id')
  async deleteItem(@Param('id') id: number) {
    return CoreUtils.handleRequest(async () => {
      await this.itemService.deleteAnItem(id);
      return {
        message: 'Item deleted successfully',
        data: null,
      };
    });
  }

  // @ApiOperation({ summary: 'Update item condition' })
  // @ApiBody({ type: ItemConditionStatusUpdateDto })
  // @Patch('condition/:itemId')
  // async updateItemCondition(
  //   @Body() conditionDto: ItemConditionStatusUpdateDto,
  //   @Param('itemId') itemId: number,
  //   @CurrentUser() user: User,
  // ) {
  //   return CoreUtils.handleRequest(async () => {
  //     const item = await this.classMapper.mapAsync(
  //       conditionDto,
  //       ItemConditionStatusUpdateDto,
  //       Item,
  //     );
  //     const updatedItem = await this.itemService.modifyItemCondition(
  //       itemId,
  //       item.condition,
  //       user,
  //     );

  //     // Get store and department information
  //     const [store, department] = await Promise.all([
  //       this.storeService.findByPk(updatedItem.storeId),
  //       this.departmentService.findByPk(updatedItem.departmentId),
  //     ]);

  //     // Map to ItemDto with store and department information
  //     const itemDto = await this.classMapper.mapAsync(
  //       updatedItem,
  //       Item,
  //       ItemDto,
  //     );
  //     itemDto.store = await this.classMapper.mapAsync(store, Store, StoreDto);
  //     itemDto.department = await this.classMapper.mapAsync(
  //       department,
  //       Department,
  //       DepartmentDto,
  //     );

  //     return {
  //       message: 'Item condition updated successfully',
  //       data: itemDto,
  //     };
  //   });
  // }
}
