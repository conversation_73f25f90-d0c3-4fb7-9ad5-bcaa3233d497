import { AutoMap } from '@automapper/classes';
import { IsEmail } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Gender } from '../../../../common/enums/gender.enum';

export class OnboardUserDto {
  @AutoMap()
  @IsEmail()
  @ApiProperty()
  email: string;

  @AutoMap()
  @ApiProperty()
  firstName?: string;

  @AutoMap()
  @ApiProperty()
  lastName?: string;

  @AutoMap()
  @ApiProperty()
  phoneNumber?: string;

  @AutoMap()
  @ApiProperty()
  password: string;

  @AutoMap()
  @ApiProperty()
  department: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    enum: Object.values(Gender),
    name: 'userType',
    description: 'Possible',
  })
  gender: Gender;
}
