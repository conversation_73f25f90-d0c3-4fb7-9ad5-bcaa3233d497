import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateUserTable1721986781384 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('user', 'department');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'user',
      new TableColumn({
        name: 'department',
        type: 'varchar',
        isNullable: true,
      }),
    );
  }
}
