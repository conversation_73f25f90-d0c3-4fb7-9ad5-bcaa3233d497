import { <PERSON>du<PERSON> } from '@nestjs/common';
import { GeneratorLogService } from './generator-log.service';
import { GeneratorLogController } from './generator-log.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GeneratorLog } from './entities/generator-log.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { GeneratorLogMapperService } from './generator-log.mapper.service';
import { GeneratorLogValidator } from './generator-log.validator';

@Module({
  controllers: [GeneratorLogController],
  exports: [GeneratorLogService],
  imports: [TypeOrmModule.forFeature([GeneratorLog]), AutomapperModule],
  providers: [
    GeneratorLogService,
    GeneratorLogMapperService,
    GeneratorLogValidator,
  ],
})
export class GeneratorLogModule {}
