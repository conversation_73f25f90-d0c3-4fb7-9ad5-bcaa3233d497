import {
  MigrationInterface,
  QueryRunner,
  <PERSON>C<PERSON>umn,
  TableForeign<PERSON>ey,
} from 'typeorm';

export class CreateStoreItemFk1721738271522 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'item',
      new TableColumn({
        name: 'store_id',
        type: 'bigint',
      }),
    );

    await queryRunner.createForeignKey(
      'item',
      new TableForeignKey({
        columnNames: ['store_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'store',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('item');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('store_id') !== -1,
    );
    await queryRunner.dropForeignKey('item', foreignKey);
    await queryRunner.dropColumn('item', 'store_id');
  }
}
