import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Condition } from '@common/enums/condition.enum';

export class ReturnItemAuditDto {
  @AutoMap()
  @ApiProperty()
  storeName: string;

  @AutoMap()
  @ApiProperty()
  itemId: number;

  @AutoMap()
  @ApiProperty()
  quantityReturned: number;

  @AutoMap()
  @ApiProperty()
  returnedDate: Date;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    enum: Object.values(Condition),
    name: 'conditionAfterReturned',
    description: 'Possible item conditions',
  })
  conditionAfterReturned: Condition;
}
