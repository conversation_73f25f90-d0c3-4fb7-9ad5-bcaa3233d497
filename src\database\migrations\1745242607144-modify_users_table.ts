import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class ModifyUsersTable1745242607144 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'user',
      new TableColumn({
        name: 'has_default_password',
        type: 'boolean',
        default: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('user', 'has_default_password');
  }
}
