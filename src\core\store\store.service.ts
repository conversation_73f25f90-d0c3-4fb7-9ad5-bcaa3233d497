import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityStatus } from '@common/entities/base.entity';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { LoggerService } from '@common/logger/logger.service';
import { PaginationQueryParams } from '@common/types';
import { User } from '@core/security/user/entities/user.entity';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  IPaginationOptions,
  paginate,
  Pagination,
} from 'nestjs-typeorm-paginate';
import { ILike, Repository } from 'typeorm';
import { CreateStoreDto } from './dto/create-store.dto';
import { StoreDto } from './dto/store.dto';
import { UpdateStoreDto } from './dto/update-store.dto';
import { Store } from './entities/store.entity';
import { StoreValidator } from './store.validator';

@Injectable()
export class StoreService {
  constructor(
    @InjectRepository(Store)
    private readonly storeRepository: Repository<Store>,
    private readonly storeValidator: StoreValidator,
    private readonly logger: LoggerService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(StoreService.name);
  }

  async createStore(createStoreDto: CreateStoreDto, user: User) {
    const store: Store = await this.classMapper.mapAsync(
      createStoreDto,
      CreateStoreDto,
      Store,
    );
    store.createdBy = `${user.firstName} ${user.lastName}`;
    await this.storeValidator.validate(store, DatabaseAction.CREATE);
    await this.storeRepository.save(store);
  }

  async updateStore(id: number, updateStoreDto: UpdateStoreDto, user: User) {
    const store = await this.findByPk(id);
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    await this.classMapper.mutateAsync(updateStoreDto, store, StoreDto, Store);
    store.updatedBy = `${user.firstName} ${user.lastName}`;
    await this.update(store);
  }

  async getStore(id: number) {
    const store = await this.findByPk(id);
    if (!store) {
      throw new NotFoundException('Store not found');
    }
    return await this.classMapper.mapAsync(store, Store, StoreDto);
  }

  async getPaginatedStores(params: PaginationQueryParams, route: string) {
    const { page, limit, search, filter } = params;
    const where = {};
    if (search) {
      where['name'] = ILike(`%${search}%`);
    }
    if (filter) {
      where['status'] = filter;
    }

    const options = {
      page,
      limit: limit > 100 ? 100 : limit, // limit the pagination to 100
      route,
    };

    const pagination = await this.paginate(options, where);

    const dtoList = await this.classMapper.mapArrayAsync(
      pagination.items,
      Store,
      StoreDto,
    );

    return new Pagination(dtoList, pagination.meta, pagination.links);
  }

  async update(store: Store): Promise<Store | undefined> {
    await this.storeValidator.validate(store, DatabaseAction.UPDATE);
    return this.storeRepository.save(store);
  }

  async findAll(): Promise<Array<Store>> {
    return this.storeRepository.find({ relations: { location: true } });
  }

  async findByPk(id: number): Promise<Store> {
    return this.storeRepository.findOneBy({ id });
  }

  async findFirstBy(where: any): Promise<Store> {
    return this.storeRepository.findOneBy(where);
  }

  async delete(id: number): Promise<void> {
    await this.storeRepository.delete(id);
  }

  async activate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const store: Store = await this.findByPk(id);
        store.status = EntityStatus.ACTIVE;
        await this.update(store);
      }),
    );
  }

  async deactivate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const store: Store = await this.findByPk(id);
        store.status = EntityStatus.INACTIVE;
        await this.update(store);
      }),
    );
  }

  async paginate(
    options: IPaginationOptions,
    where?: any,
  ): Promise<Pagination<Store>> {
    if (where && Object.keys(where).length > 0) {
      return paginate<Store>(this.storeRepository, options, {
        where,
        order: {
          createdAt: 'DESC',
        },
      });
    }
    return paginate<Store>(this.storeRepository, options);
  }

  // server side search without pagination
  async search(query: string): Promise<Array<Store>> {
    return await this.storeRepository.find({
      where: [{ name: ILike(`%${query}%`) }],
    });
  }

  // server side search with pagination
  async paginatedSearch(
    query: string,
    page = 1,
    limit = 10,
  ): Promise<Array<Store>> {
    const [results] = await this.storeRepository.findAndCount({
      where: [{ name: ILike(`%${query}%`) }],
      skip: (page - 1) * limit,
      take: limit,
    });
    return results;
  }

  async fetchStoresByPks(ids: Array<number>): Promise<Array<Store>> {
    return await Promise.all(ids.map((id) => this.findByPk(id)));
  }
}
