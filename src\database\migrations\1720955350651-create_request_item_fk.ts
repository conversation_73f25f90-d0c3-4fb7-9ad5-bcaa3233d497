import { MigrationInterface, QueryRunner, TableForeign<PERSON><PERSON> } from 'typeorm';

export class CreateRequestItemFk1720955350651 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create FX for request_audit table
    await queryRunner.createForeignKey(
      'request_audit',
      new TableForeignKey({
        columnNames: ['summary_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'request_summary',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the foreign key first
    const table = await queryRunner.getTable('request_audit');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('summary_id') !== -1,
    );
    await queryRunner.dropForeignKey('request_audit', foreignKey);
  }
}
