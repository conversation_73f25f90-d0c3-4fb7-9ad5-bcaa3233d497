import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateDepartment1721205444993 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'department',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
          { name: 'name', type: 'varchar' },
          { name: 'hod_name', type: 'varchar', isNullable: true },
          { name: 'hod_phone', type: 'varchar', isNullable: true },
          { name: 'hod_email', type: 'varchar', isNullable: true },
        ],
      }),
      true,
    );

    // Add an index to the table.
    await queryRunner.createIndex(
      'department',
      new TableIndex({
        name: 'IDX_DEPARTMENT_NAME',
        columnNames: ['name'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the index first before dropping the table.
    await queryRunner.dropIndex('department', 'IDX_DEPARTMENT_NAME');
    await queryRunner.dropTable('department');
  }
}
