import {
  MigrationInterface,
  QueryRunner,
  <PERSON><PERSON><PERSON>umn,
  TableForeignKey,
} from 'typeorm';

export class CreateUserAddressFk1720828301585 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add a column to the user table to reference the address table.
    await queryRunner.addColumn(
      'user',
      new TableColumn({
        name: 'address_id',
        type: 'bigint',
        isNullable: true,
      }),
    );

    // Add a foreign key to the address table.
    await queryRunner.createForeignKey(
      'user',
      new TableForeignKey({
        columnNames: ['address_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'address',
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop FK, user table, and its index
    const userTable = await queryRunner.getTable('user');
    const fk = userTable.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('address_id') !== -1,
    );
    await queryRunner.dropForeignKey('user', fk);
    await queryRunner.dropColumn('user', 'address_id');
  }
}
