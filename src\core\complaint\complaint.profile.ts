import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingProfile,
} from '@automapper/core';
import { Complaint } from './entities/complaint.entity';
import { CreateComplaintDto } from './dto/create-complaint.dto';
import { ComplaintDto } from './dto/complaint.dto';
import { ComplaintTableDto } from './dto/complaint-table.dto';
import { ComplaintSummaryDto } from '@core/complaint/complaint-summary/dto/complaint-summary.dto';
import { ComplaintSummary } from '@core/complaint/complaint-summary/entities/complaint-summary.entity';

@Injectable()
export class ComplaintProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, ComplaintSummary, ComplaintSummaryDto);
      createMap(
        mapper,
        Complaint,
        ComplaintDto,
        forMember(
          async (destination) => {
            return destination.summary;
          },
          mapFrom((source) =>
            this.mapper.mapAsync(
              source.summary,
              ComplaintSummary,
              ComplaintSummaryDto,
            ),
          ),
        ),
      );
      createMap(mapper, ComplaintDto, Complaint);
      createMap(mapper, CreateComplaintDto, Complaint);
      createMap(mapper, Complaint, ComplaintTableDto);
    };
  }
}
