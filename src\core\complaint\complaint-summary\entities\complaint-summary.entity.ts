import { Column, <PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { AbstractEntity } from '@common/entities/base.entity';
import { ComplaintStatus } from '@common/enums/complaint-status.enum';
import { Complaint } from '@core/complaint/entities/complaint.entity';
import { Request } from '@core/request/entities/request.entity';

@Entity({ name: 'complaint_summary' })
export class ComplaintSummary extends AbstractEntity {
  @AutoMap()
  @Column({
    name: 'complaint_status',
    type: 'enum',
    enum: ComplaintStatus,
    default: ComplaintStatus.DEFAULT,
  })
  complaintStatus: ComplaintStatus;

  @AutoMap()
  @Column({ name: 'attended_to', type: 'boolean', default: false })
  attendedTo: boolean;

  @AutoMap()
  @Column({ name: 'date_resolved', type: 'timestamp' })
  dateResolved: Date;

  @AutoMap()
  @Column({ name: 'resolved_by', type: 'varchar', length: 255 })
  resolvedBy: string;

  @AutoMap()
  @OneToOne(() => Request, (request) => request.summary, {
    onDelete: 'CASCADE',
    orphanedRowAction: 'delete',
  })
  @JoinColumn({ name: 'complaint_id' })
  complaint: Complaint;
}
