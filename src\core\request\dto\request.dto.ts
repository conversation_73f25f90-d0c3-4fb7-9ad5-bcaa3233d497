import { EntityDto } from '@common/dto/base.dto';
import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail } from 'class-validator';
import { RequestSummaryDto } from '@core/request/request-summary/dto/request-summary.dto';
import { ItemAuditDto } from '@core/store/item-audit/dto/item-audit.dto';

export class RequestDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  requesterName: string;

  @AutoMap()
  @ApiProperty()
  @IsEmail()
  requesterEmail: string;

  @AutoMap()
  @ApiProperty()
  requesterPhone: string;

  @AutoMap()
  @ApiProperty()
  isMinistry: boolean;

  @AutoMap()
  @ApiProperty()
  ministryName: string;

  @AutoMap()
  @ApiProperty()
  isChurch: boolean;

  @AutoMap()
  @ApiProperty()
  churchName: string;

  @AutoMap()
  @ApiProperty()
  requesterDepartmentId: number;

  @AutoMap()
  @ApiProperty()
  @IsEmail()
  requesterHodName: string;

  @AutoMap()
  @ApiProperty()
  requesterHodEmail: string;

  @AutoMap()
  @ApiProperty()
  locationOfUse: string;

  @AutoMap()
  @ApiProperty()
  store: string;

  @AutoMap()
  @ApiProperty()
  durationOfUse: Date;

  @AutoMap()
  @ApiProperty()
  dateOfReturn: Date;

  @AutoMap()
  @ApiProperty()
  descriptionOfRequest: string;

  @AutoMap()
  @ApiProperty()
  summary: RequestSummaryDto;

  @AutoMap()
  @ApiProperty({ type: ItemAuditDto, isArray: true })
  items?: Array<ItemAuditDto>;
}
