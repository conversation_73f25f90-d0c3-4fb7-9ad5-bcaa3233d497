import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateRequestTable1723061623595 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.changeColumn(
      'request',
      'requester_department',
      new TableColumn({
        name: 'requester_department_id',
        type: 'bigint',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('request', 'requester_department_id');
    await queryRunner.addColumn(
      'request',
      new TableColumn({
        name: 'requester_department',
        type: 'varchar',
      }),
    );
  }
}
