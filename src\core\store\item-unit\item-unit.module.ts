import { AutomapperModule } from '@automapper/nestjs';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ItemUnit } from './entities/item-unit.entity';
import { ItemUnitController } from './item-unit.controller';
import { ItemUnitService } from './item-unit.service';

@Module({
  controllers: [ItemUnitController],
  exports: [ItemUnitService],
  imports: [TypeOrmModule.forFeature([ItemUnit]), AutomapperModule],
  providers: [ItemUnitService],
})
export class ItemUnitModule {}
