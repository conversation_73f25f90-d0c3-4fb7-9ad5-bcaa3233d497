import {
  MigrationInterface,
  QueryRunner,
  <PERSON><PERSON><PERSON>umn,
  TableForeign<PERSON>ey,
} from 'typeorm';

export class CreateUserRoleFk1721981839426 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add a column to the user table to reference the role table.
    await queryRunner.addColumn(
      'user',
      new TableColumn({
        name: 'role_id',
        type: 'bigint',
        isNullable: true,
      }),
    );

    // Add a foreign key to the user table.
    await queryRunner.createForeignKey(
      'user',
      new TableForeignKey({
        columnNames: ['role_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'role',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const userTable = await queryRunner.getTable('user');
    const fk = userTable.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('role_id') !== -1,
    );
    await queryRunner.dropForeignKey('user', fk);
    await queryRunner.dropColumn('user', 'role_id');
  }
}
