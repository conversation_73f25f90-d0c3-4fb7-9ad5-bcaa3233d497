import { Module } from '@nestjs/common';
import { RequestAuditProfile } from './request-audit.profile';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RequestAudit } from './entities/request-audit.entity';
import { AutomapperModule } from '@automapper/nestjs';

@Module({
  controllers: [],
  exports: [],
  imports: [TypeOrmModule.forFeature([RequestAudit]), AutomapperModule],
  providers: [RequestAuditProfile],
})
export class RequestAuditModule {}
