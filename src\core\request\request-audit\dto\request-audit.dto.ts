import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';

export class RequestAuditDto {
  @AutoMap()
  @ApiProperty()
  assigned: boolean;

  @AutoMap()
  @ApiProperty()
  assigner: string;

  @AutoMap()
  @ApiProperty()
  assigneeName: string;

  @AutoMap()
  @ApiProperty()
  assignee: number;

  @AutoMap()
  @ApiProperty()
  dateAssigned: Date;

  @AutoMap()
  @ApiProperty()
  completed: boolean;

  @AutoMap()
  @ApiProperty()
  completedDate: Date;

  @AutoMap()
  @ApiProperty()
  completedBy: string;

  @AutoMap()
  @ApiProperty()
  collected: boolean;

  @AutoMap()
  @ApiProperty()
  collectedDate: Date;

  @AutoMap()
  @ApiProperty()
  collectedBy: string;
}
