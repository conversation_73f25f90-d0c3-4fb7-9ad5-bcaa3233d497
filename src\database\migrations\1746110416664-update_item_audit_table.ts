import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateItemAuditTable1746110416664 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('item_audit', [
      new TableColumn({
        name: 'store_id',
        type: 'bigint',
        isNullable: true,
      }),
      new TableColumn({
        name: 'item_name',
        type: 'varchar',
        isNullable: true,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('item_audit', 'store_id');
    await queryRunner.dropColumn('item_audit', 'item_name');
  }
}
