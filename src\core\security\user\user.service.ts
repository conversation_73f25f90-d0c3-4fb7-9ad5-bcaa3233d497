import { <PERSON>per } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityStatus } from '@common/entities/base.entity';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { Gender } from '@common/enums/gender.enum';
import { Role } from '@common/enums/roles.enum';
import { LoggerService } from '@common/logger/logger.service';
import { PaginationQueryParams } from '@common/types';
import { CoreUtils } from '@common/utils/core.utils';
import { MailService } from '@core/notification/mail-service/mail.service';
import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { paginate, Pagination } from 'nestjs-typeorm-paginate';
import { ILike, Repository } from 'typeorm';
import { RoleService } from '../role/role.service';
import { InitiateUserDto } from './dto/initiate-user.dto';
import { UserPaginationDto } from './dto/user-pagination.dto';
import { User } from './entities/user.entity';
import { UserValidator } from './user.validator';
import { CoreConstants } from '@common/utils/core.constants';
import { DepartmentService } from '@core/security/department/department.service';

@Injectable()
export class UserService {
  privateKey: string;
  publicKey: string;
  backOfficeUrl: string;
  userOnboardPath: string;
  passwordResetPath: string;

  constructor(
    @InjectRepository(User) private userRepository: Repository<User>,
    private userValidator: UserValidator,
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly mailService: MailService,
    private readonly departmentService: DepartmentService,
    private readonly roleService: RoleService,
  ) {
    this.logger.setContext(UserService.name);
    this.privateKey = this.configService.get<string>('keys.privateKey');
    this.publicKey = this.configService.get<string>('keys.publicKey');
    this.backOfficeUrl = this.configService.get<string>('backOfficeUrl');
    this.userOnboardPath = this.configService.get<string>('userOnboardingPath');
    this.passwordResetPath =
      this.configService.get<string>('passwordResetPath');
  }

  async create(user: User): Promise<User | undefined> {
    await this.userValidator.validate(user, DatabaseAction.CREATE);
    return await this.userRepository.save(user);
  }

  async update(user: User): Promise<User | undefined> {
    await this.userValidator.validate(user, DatabaseAction.UPDATE);
    return await this.userRepository.save(user);
  }

  async findAll(): Promise<Array<User>> {
    return await this.userRepository.find({
      relations: ['address'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByPk(userId: number): Promise<User> {
    return await this.userRepository.findOneBy({ id: userId });
  }

  async findByEmail(email: string): Promise<User> {
    return await this.userRepository.findOneBy({ email: email });
  }

  async findFirstBy(where: any): Promise<User> {
    return await this.userRepository.findOne(where);
  }

  /**
   * Get all super admins from the database.
   */
  async getAllSuperAdmins(): Promise<Array<User>> {
    return await this.userRepository.find({
      where: { role: { name: CoreConstants.SUPER_ADMIN_ROLE } },
    });
  }

  async deleteUser(userId: number) {
    return await this.userRepository.delete(userId);
  }

  async getPaginatedUsers(params: PaginationQueryParams, route: string) {
    const { page, limit, search, filter } = params;
    const where = {};
    if (search) {
      where['email'] = ILike(`%${search}%`);
      where['firstName'] = ILike(`%${search}%`);
      where['lastName'] = ILike(`%${search}%`);
    }

    if (filter) {
      where['status'] = filter;
      where['role.name'] = filter;
    }

    const options = {
      page,
      limit: limit > 100 ? 100 : limit, // limit the pagination to 100
      route,
    };

    const pagination = await paginate<User>(this.userRepository, options, {
      where,
      order: {
        createdAt: 'DESC',
      },
    });

    const dtoList = await this.classMapper.mapArrayAsync(
      pagination.items,
      User,
      UserPaginationDto,
    );

    return new Pagination(dtoList, pagination.meta, pagination.links);
  }

  async activate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const user: User = await this.findByPk(id);
      user.status = EntityStatus.ACTIVE;
      await this.update(user);
    });
  }

  async deactivate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const user: User = await this.findByPk(id);
      user.status = EntityStatus.INACTIVE;
      await this.update(user);
    });
  }

  /**
   * server side search without pagination
   * @param query
   */
  async search(query: string): Promise<Array<User>> {
    return await this.userRepository.find({
      where: [
        { firstName: ILike(`%${query}%`) },
        { email: ILike(`%${query}%`) },
        { lastName: ILike(`%${query}%`) },
      ],
    });
  }

  /**
   * server side search with pagination
   * @param query
   * @param page
   * @param limit
   */
  async paginatedSearch(
    query: string,
    page = 1,
    limit = 10,
  ): Promise<Array<User>> {
    const [results] = await this.userRepository.findAndCount({
      where: [
        { firstName: ILike(`%${query}%`) },
        { email: ILike(`%${query}%`) },
        { lastName: ILike(`%${query}%`) },
      ],
      skip: (page - 1) * limit,
      take: limit,
    });
    return results;
  }

  /**
   * Generate user registration url
   * @param {string} token - user token
   * @param email
   * @returns {string} - registration url
   */
  async generateRegistrationUrl(token: string, email: string): Promise<string> {
    return `${this.backOfficeUrl}/${this.userOnboardPath}/${email}?token=${token}`;
  }

  async generatePasswordResetUrl(token: string): Promise<string> {
    return `${this.backOfficeUrl}/${this.passwordResetPath}?token=${token}`;
  }

  async initiateUser(initiateUserDto: InitiateUserDto, currentUser: User) {
    const [existingRole, existingDepartment, existingUser] = await Promise.all([
      this.roleService.findByPk(initiateUserDto.role),
      this.departmentService.findByPk(initiateUserDto.departmentId),
      this.findByEmail(initiateUserDto.email),
    ]);

    if (!existingRole) {
      throw new NotFoundException('Role not found');
    }

    if (initiateUserDto.departmentId && !existingDepartment) {
      throw new NotFoundException('Department not found');
    }

    if (existingUser) {
      throw new ConflictException(
        `User with email ${initiateUserDto.email} already exists`,
      );
    }

    const user = await this.classMapper.mapAsync(
      initiateUserDto,
      InitiateUserDto,
      User,
    );

    user.role = existingRole;
    user.department = initiateUserDto.departmentId ? existingDepartment : null;

    if (user.role.name === Role.HOD) {
      existingDepartment.hodName = `${user.firstName} ${user.lastName}`;
      existingDepartment.hodPhone = user.phoneNumber;
      existingDepartment.hodEmail = user.email;
      await this.departmentService.update(existingDepartment);
    }
    user.createdBy = `${currentUser.firstName} ${currentUser.lastName}`;

    user.gender = Gender.DEFAULT;
    user.status = EntityStatus.INACTIVE;
    const randomPassword = CoreUtils.generateRandomPassword(12);
    user.password = CoreUtils.hash(randomPassword);
    user.token = CoreUtils.generateToken().replace(/-/g, '');
    user.expiresAt = CoreUtils.addSecondsToCurrentDate(1800).getTime();

    const initiatedUser = await this.create(user);

    // Generate registration URL
    const registrationUrl = await this.generateRegistrationUrl(
      initiatedUser.token,
      user.email,
    );

    // Send email
    await this.mailService.sendSmtpMail(
      initiatedUser.email,
      'Complete your registration',
      `Kindly click the link to complete your registration: ${registrationUrl} \nYour default password is ${randomPassword} \n Remain blessed.`,
    );
  }

  async updateTokenAndResendVerificationEmail(email: string) {
    const user = await this.findByEmail(email);
    if (!user) {
      throw new NotFoundException('User with email not found');
    }

    if (user.isVerified) {
      throw new ConflictException('User is already verified');
    }

    // Regenerate token and expiry time
    user.token = CoreUtils.generateToken().replace(/-/g, '');
    user.expiresAt = CoreUtils.addSecondsToCurrentDate(1800).getTime();

    const registrationUrl = await this.generateRegistrationUrl(
      user.token,
      user.email,
    );

    await this.update(user);

    await this.mailService.sendSmtpMail(
      user.email,
      'Complete your registration',
      `Kindly click the link to complete your registration: ${registrationUrl} \nYour default password is ${user.password} \n Remain blessed.`,
    );
  }

  async fetchAllUsersByRole(role: string): Promise<Array<User>> {
    return Promise.resolve(
      this.userRepository.find({
        where: { role: ILike(`%${role}%`) },
      }),
    );
  }

  async fetchUserByLastName(lastName: string): Promise<User> {
    return Promise.resolve(
      await this.userRepository.findOne({ where: { lastName: lastName } }),
    );
  }

  async fetchUserByToken(token: string): Promise<User> {
    return Promise.resolve(
      await this.userRepository.findOne({ where: { token: token } }),
    );
  }

  delete(id: number): Promise<void> {
    return Promise.resolve(undefined);
  }

  async fetchUsersByRoleId(roleId: number): Promise<Array<User>> {
    return await this.userRepository.find({
      where: { role: { id: roleId } },
      relations: ['role', 'department'],
    });
  }
}
