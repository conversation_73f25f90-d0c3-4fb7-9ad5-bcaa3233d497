import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class CreateItemDto {
  @AutoMap()
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @AutoMap(() => Number)
  @ApiProperty()
  @IsNumber()
  actualQuantity: number;

  // @AutoMap(() => Number)
  // @ApiProperty()
  // @IsNumber()
  // storeId: number;

  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
  })
  @IsBoolean()
  fragile: boolean;

  // @AutoMap(() => String)
  // @ApiProperty({
  //   description: 'Possible conditions of an item',
  //   enum: Object.values(Condition),
  //   name: 'condition',
  //   type: 'string',
  //   default: Condition.NOT_SPECIFIED,
  // })
  // @IsEnum(Condition)
  // condition: Condition;

  @AutoMap(() => Number)
  @ApiProperty()
  @IsNumber()
  departmentId: number;
}
