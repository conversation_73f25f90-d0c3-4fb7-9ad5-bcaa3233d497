import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingProfile,
} from '@automapper/core';
import { RequestDto } from '@core/request/dto/request.dto';
import { Request } from '@core/request/entities/request.entity';
import { CreateRequestDto } from '@core/request/dto/create-request.dto';
import { RequestTableDto } from '@core/request/dto/request-table.dto';
import { RequestDetailDto } from '@core/request/dto/request-detail.dto';
import { ItemAudit } from '@core/store/item-audit/entities/item-audit.entity';
import { ItemAuditDto } from '@core/store/item-audit/dto/item-audit.dto';
import { CreateItemAuditDto } from '@core/store/item-audit/dto/create-item-audit.dto';
import { RequestSummary } from '@core/request/request-summary/entities/request-summary.entity';
import { RequestSummaryDto } from '@core/request/request-summary/dto/request-summary.dto';

@Injectable()
export class RequestMapper extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, RequestDto, Request);
      createMap(
        mapper,
        Request,
        RequestDto,
        forMember(
          (destination) => destination.items,
          mapFrom((source) =>
            this.mapper.mapArray(source.items, ItemAudit, ItemAuditDto),
          ),
        ),
      );
      createMap(
        mapper,
        CreateRequestDto,
        Request,
        forMember(
          (destination) => destination.requesterDepartmentId,
          mapFrom((source) => Number(source.requesterDepartmentId)),
        ),
        forMember(
          (destination) => destination.dateOfReturn,
          mapFrom((source) =>
            source.dateOfReturn ? new Date(source.dateOfReturn) : null,
          ),
        ),
        forMember(
          (destination) => destination.items,
          mapFrom((source) => {
            if (!source.items) return [];
            return this.mapper.mapArray(
              source.items,
              CreateItemAuditDto,
              ItemAudit,
            );
          }),
        ),
      );
      createMap(mapper, RequestTableDto, Request);
      createMap(mapper, Request, RequestTableDto);
      createMap(
        mapper,
        Request,
        RequestDetailDto,
        forMember(
          (destination) => destination.items,
          mapFrom((source) =>
            this.mapper.mapArray(source.items, ItemAudit, ItemAuditDto),
          ),
        ),
        forMember(
          (destination) => destination.summary,
          mapFrom((source) =>
            this.mapper.map(source.summary, RequestSummary, RequestSummaryDto),
          ),
        ),
      );
    };
  }
}
