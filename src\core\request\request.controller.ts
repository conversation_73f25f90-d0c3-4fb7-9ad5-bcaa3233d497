import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { CurrentUser } from '@common/decorators/current-user.decorator';
import { Public } from '@common/decorators/public.decorator';
import { CoreUtils } from '@common/utils/core.utils';
import { User } from '@core/security/user/entities/user.entity';
import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import {
  ApiBody,
  ApiExcludeEndpoint,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { Pagination } from 'nestjs-typeorm-paginate';
import { ActivateRequestDto } from './dto/activate-request.dto';
import { AssignRequestDto } from './dto/assign-request.dto';
import { CreateRequestDto } from './dto/create-request.dto';
import { DeactivateRequestDto } from './dto/deactivate-request.dto';
import { RequestDto } from './dto/request.dto';
import { RequestService } from './request.service';
import { LoggerService } from '@common/logger/logger.service';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { Request } from '@core/request/entities/request.entity';
import { RequestDetailDto } from '@core/request/dto/request-detail.dto';
import { CollectRequestItemsDto } from './dto/collect-request-items.dto';
import { ReturnRequestDto } from './dto/return-request.dto';

@ApiTags('Request')
@Controller({
  path: 'request',
  version: '1',
})
export class RequestController {
  constructor(
    private readonly requestService: RequestService,
    private readonly logger: LoggerService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(RequestController.name);
  }

  @Public()
  @ApiOperation({ summary: 'Create a new request' })
  @ApiBody({ type: CreateRequestDto })
  @HttpCode(HttpStatus.CREATED)
  @Post('new')
  async newRequest(@Body() requestDto: CreateRequestDto) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.classMapper.mapAsync(
        requestDto,
        CreateRequestDto,
        Request,
      );
      await this.requestService.create(data);
      return { message: 'Request created successfully', data: null };
    });
  }

  // @Public()
  @ApiOperation({ summary: 'Retrieve a request by ID' })
  @ApiOkResponse({
    description: 'Request details retrieved successfully',
    type: Request,
    isArray: false,
  })
  @Get('detail/:id')
  async retrieveARequest(@Param('id') id: number) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.requestService.getRequest(id);

      const requestDetails = await this.classMapper.mapAsync(
        data,
        Request,
        RequestDetailDto,
      );

      const response = {
        ...requestDetails,
        summary: {
          ...requestDetails.summary,
          audit: {
            ...requestDetails.summary?.audit,
            assigneeName: data.summary.audit.assigneeName,
          },
        },
      };

      return { message: 'Request retrieved successfully', data: response };
    });
  }

  @ApiOperation({ summary: 'Make request(s) active.' })
  @ApiBody({ type: ActivateRequestDto })
  @Patch('activate')
  async activate(@Body() body: ActivateRequestDto) {
    return CoreUtils.handleRequest(async () => {
      await this.requestService.activate(body.ids);
      return {
        message: `Request${
          body.ids.length > 1 ? 's' : ''
        } activated successfully`,
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Make request(s) inactive.' })
  @ApiBody({ type: DeactivateRequestDto })
  @Patch('deactivate')
  async deactivate(@Body() body: DeactivateRequestDto) {
    return CoreUtils.handleRequest(async () => {
      await this.requestService.deactivate(body.ids);
      return {
        message: `Request${
          body.ids.length > 1 ? 's' : ''
        } deactivated successfully`,
        data: null,
      };
    });
  }

  @Get()
  @ApiOperation({
    summary: 'Get paginated list of requests, or all request per department',
  })
  @ApiQuery({ name: 'page', required: true, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: true, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'filter', required: false, type: String })
  @ApiQuery({ name: 'id', required: false, type: Number })
  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Successful response with paginated requests',
    type: Pagination,
  })
  async getAllRequests(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit,
    @Query('search') search: string,
    @Query('filter') filter: string,
    @Query('id') id: number,
    @CurrentRoute() route: string,
  ) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.requestService.getPaginatedRequests(
        {
          page,
          limit,
          search,
          filter,
          id,
        },
        route,
      );
      return { message: 'Requests retrieved successfully', data };
    });
  }

  @ApiOperation({ summary: 'Assign a request to a user' })
  @ApiBody({ type: AssignRequestDto })
  @Patch('assign/:requestId')
  async assignRequest(
    @Param('requestId') requestId: number,
    @Body() assignRequestDto: AssignRequestDto,
    @CurrentUser() user: User,
  ) {
    return CoreUtils.handleRequest(async () => {
      await this.requestService.assignRequest(
        requestId,
        assignRequestDto,
        user,
      );
      return { message: 'Request assigned successfully', data: null };
    });
  }

  //TODO: Only the HOD can approve a request - Protect this route
  @ApiOperation({ summary: 'Approve a request' })
  @Patch('/approve/:requestId')
  async approveRequest(@Param('requestId') requestId: number) {
    return CoreUtils.handleRequest(async () => {
      await this.requestService.approveRequest(requestId);
      return { message: 'Request approved successfully', data: null };
    });
  }

  //TODO: Only the HOD can decline a request - Protect this route
  @ApiOperation({ summary: 'Decline a request' })
  @Patch('/decline/:requestId')
  async declineRequest(@Param('requestId') requestId: number) {
    return CoreUtils.handleRequest(async () => {
      await this.requestService.declineRequest(requestId);
      return { message: 'Request declined successfully', data: null };
    });
  }

  @ApiOperation({ summary: 'Collect request items' })
  @Patch('/release/:requestId')
  async markRequestAsCollected(
    @Param('requestId') requestId: number,
    @Body() collectRequestItemsDto: CollectRequestItemsDto,
  ) {
    return CoreUtils.handleRequest(async () => {
      await this.requestService.markRequestAsCollected(
        requestId,
        collectRequestItemsDto,
      );
      return { message: 'Request items released successfully', data: null };
    });
  }

  @ApiOperation({ summary: 'Return a request' })
  @Patch('/return-item/:requestId')
  async markRequestAsCompleted(
    @Param('requestId') requestId: number,
    @Body() returnRequestItemsDto: ReturnRequestDto,
  ) {
    return CoreUtils.handleRequest(async () => {
      await this.requestService.markRequestAsCompleted(
        requestId,
        returnRequestItemsDto,
      );
      return {
        message: 'Request items returned successfully',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Retrieve all items in a request' })
  @Get('items/:requestId')
  async retrieveAllRequestedItems(@Param('requestId') requestId: number) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.requestService.retrieveAllRequestedItems(
        requestId,
      );
      return { message: 'Requests retrieved successfully', data };
    });
  }

  @ApiExcludeEndpoint()
  @ApiOperation({ summary: 'Retrieve all requests by department ID' })
  @Get('/all/:departmentId')
  @ApiOkResponse({
    description: 'Requests retrieved successfully',
    type: RequestDto,
    isArray: true,
  })
  async retrieveAllRequestsByDepartmentId(
    @Param('departmentId') departmentId: number,
  ) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.requestService.fetchAllRequestsByDepartmentId(
        departmentId,
      );
      return { message: 'Requests retrieved successfully', data };
    });
  }

  @ApiOperation({ summary: 'Search for requests' })
  @ApiQuery({ name: 'q', required: true, type: String, example: 'john' })
  @Get('/search')
  async searchRequests(@Query('q') query: string) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.requestService.search(query);
      return {
        message: 'Requests retrieved successfully',
        data,
      };
    });
  }

  @ApiOperation({ summary: 'Retrieve all requests by user ID' })
  @ApiExcludeEndpoint()
  @Delete('/request/:id')
  async deleteRequest(@Param('id') id: number) {
    return CoreUtils.handleRequest(async () => {
      await this.requestService.deleteRequest(id);
      return {
        message: 'Request deleted successfully',
        data: null,
      };
    });
  }

  @ApiOperation({
    summary: 'Retrieve paginated requests assigned to a specific user',
  })
  @ApiQuery({ name: 'page', required: true, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: true, type: Number, example: 10 })
  @Get('assignee/:assigneeId')
  async getPaginatedRequestsByAssignee(
    @Param('assigneeId', ParseIntPipe) assigneeId: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.requestService.getPaginatedRequestsByAssignee(
        assigneeId,
        { page, limit },
      );
      return {
        message: 'Requests retrieved successfully',
        data,
      };
    });
  }
}
